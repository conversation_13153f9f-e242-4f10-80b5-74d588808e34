// pages/index/start.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
    sn: '',
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
    if(options.q){
      var urlStr = decodeURIComponent(options.q);
      this.setData({
        sn: urlStr.substring(36, urlStr.length)
      })
    }
    this.getLaunchInfo()
	},
  getLaunchInfo(){
    let that = this
    let params = {
      kpl: that.data.sn
    }
    app.post('ajax/getLaunchInfo', params).then(res => {
      const {
        code,
        data,
        msg
      } = res //接口数据
      if (code == 1) {
        wx.reLaunch({
          url: '/pages/index/index?status=' + data.type + '&kpl=' + that.data.sn,
        })
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  },
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})