/* packageB/balance/index.wxss */
/* 余额卡片 */
.top{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  padding: 30rpx 30rpx 40rpx;
  box-sizing: border-box;
}
.top .money{
  background: #88C9FF;
  border-radius: 20rpx;
  border: 2rpx solid #fff;
  padding: 30rpx 38rpx 45rpx;
  margin-bottom: 30rpx;
}
.top .money .text{
  font-size: 28rpx;
  line-height: 42rpx;
  color: #222;
  margin-bottom: 10rpx;
}
.top .money .price {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  color: #000;
}
.top .money .price view{
  font-size: 32rpx;
  line-height: 60rpx;
}
.top .money .price text{
  font-size: 60rpx;
  line-height: 84rpx;
  font-weight: bold;
}
.top .title{
  font-size: 32rpx;
  line-height: 46rpx;
  color: #000;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  gap: 8rpx;
}

.recharge-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
}

/* 余额明细 */
.balance-detail {
  padding: 361rpx 30rpx 30rpx;
}
.detail-list{
  background: #fff;
  border-radius: 20rpx;
  padding: 0 20rpx;
}
.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F8FAF9;
}

.item-icon image {
  width: 40rpx;
  height: 40rpx;
}
.item-info{
  margin-left: 20rpx;
}
.item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.item-amount {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-state image {
  width: 100%;
  height: auto;
  margin-bottom: 0 auto 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}
