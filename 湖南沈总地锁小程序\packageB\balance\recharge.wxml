<!--packageB/balance/recharge.wxml-->
<view class="container">
  <view class="top">
    <view class="nr">
      <view class="xx">每次停车需要交押金</view>
      <view class="text">多退少补 秒退微信</view>
    </view>
    <view class="price">
      <view class="text">交</view>
      <view class="text">押<text>金</text></view>
      <view class="numb">30</view>
    </view>
  </view>

  <!-- 快捷金额选择 -->
  <view class="quick-amount-section">
    <view class="quick-amount-title">
      <view>账号充值</view>
      <view>充值多优惠更多</view>
    </view>
    <view class="quick-amount-grid">
      <view class="quick-amount-item {{item.id == id ? 'selected' : ''}}" wx:for="{{quickAmounts}}" wx:key="index" data-item="{{item}}" bindtap="selectQuickAmount">
        <view class="text">充值{{item.money}}元</view>
        <view class="text">送{{item.giftmoney}}元</view>
        <view class="price">
          <view>¥</view>
          <text>{{item.total}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值说明 -->
  <view class="recharge-tips">
    <view class="tips-title">账号充值协议</view>
    <view class="tips-content">
      <view class="tip-item">1.充值金额为微信线上支付，即时到账，不支
        持线上退款,可以线下办理退款。</view>
      <view class="tip-item">2.充值成功后,金额会自动进入您的小程序账号
        您可以随时查看。</view>
      <view class="tip-item">3.退款须知：收回充值赠送，按照实际消费结
        算，退回剩余金额。</view>
    </view>
  </view>

  <view class="pay">
    <view class="title">支付方式</view>
    <view class="check active">
      <image src="/image/icon_wx.png"></image>
      <view>微信支付</view>
    </view>
  </view>
  <!-- 确认充值按钮 -->
  <view class="recharge-btn-wrapper">
    <button class="recharge-btn {{loading ? 'loading' : ''}}" bindtap="confirmRecharge" disabled="{{loading}}">
      {{loading ? '处理中...' : '确认协议并支付'}}
    </button>
  </view>
</view>