[ 2025-08-27T17:17:40+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-27T17:17:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"666","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'timestamp' => '2025-08-27 17:17:40',
)
error
[ 2025-08-27T17:17:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T17:17:40+08:00 ][ error ] array (
  'VER' => '666',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1221',
  'CH1' => '1221',
  'CH2' => '1700',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.6',
  'RSSI' => 27,
  'MT' => '2',
  'NG' => '0',
)
error
[ 2025-08-27T17:17:40+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-27T17:17:40+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 1,
  'voltage' => '11.60',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756257158,
  'last_status_updatetime' => 1756286155,
  'createtime' => 1756194042,
  'updatetime' => 1756286155,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 0
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【业务触发】检测到地锁 '110002' 车辆离开，尝试自动结束订单。
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 打印四轮地锁order信息
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'id' => 213,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082709351970903265',
  'user_id' => 5611,
  'money' => '1.60',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '3',
  'pay_time' => NULL,
  'createtime' => 1756258519,
  'returntime' => 1756286155,
  'updatetime' => 1756258519,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 8,
  'timelong_fenzhong' => 461,
  'really_money' => '1.60',
  'actreturntime' => 1756286155,
  'answer_return_time' => 1756306800,
  'overtime' => 0,
  'overtime_money' => '0.00',
  'normal_money' => '0.00',
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '1.60',
  'payment_detail' => '',
  'setmeal_deducted' => 0,
  'setmeal_log_id' => 0,
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] === 进入orderEnd1方法 ===
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 输入参数 $order_info:
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'id' => 213,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082709351970903265',
  'user_id' => 5611,
  'money' => '1.60',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '3',
  'pay_time' => NULL,
  'createtime' => 1756258519,
  'returntime' => 1756286155,
  'updatetime' => 1756258519,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 8,
  'timelong_fenzhong' => 461,
  'really_money' => '1.60',
  'actreturntime' => 1756286155,
  'answer_return_time' => 1756306800,
  'overtime' => 0,
  'overtime_money' => '0.00',
  'normal_money' => '0.00',
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '1.60',
  'payment_detail' => '',
  'setmeal_deducted' => 0,
  'setmeal_log_id' => 0,
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 初始化返回数据 $return:
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 当前时间戳 $time: 1756286260
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 当前时间格式化: 2025-08-27 17:17:40
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 检查订单状态 $order_info[status]: 1
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 订单状态为1，开始处理结束订单逻辑
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 订单创建时间 $createtime: 1756258519
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 订单创建时间格式化: 2025-08-27 09:35:19
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 时间差（秒）$time_diff_seconds: 27741
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 使用时长（小时）$timelong: 8
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 使用时长（分钟）$timelong_fenzhong: 463
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 初始化费用变量 - $overtime: 0, $overtime_money: 0, $normal_money: 0
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 查询医院信息，hospital_id: 48
log
[ 2025-08-27T17:17:40+08:00 ][ log ] orderEnd1 - 医院信息 $hospital:
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'id' => 48,
  'platform_id' => 20,
  'agent_id' => 82,
  'code' => '',
  'name' => '南山物业',
  'addr' => '南山区政府',
  'join_type' => '1',
  'fcbl' => 20.0,
  'price' => '0.20',
  'hourlong' => 1,
  'freedt' => 0,
  'notes' => NULL,
  'corpname' => NULL,
  'kefu' => '12345678',
  'logo_image' => '/uploads/20250811/af755f587cc2ab6562de3efaed08dd87.png',
  'introduce_content' => NULL,
  'status' => '1',
  'createtime' => 1665987657,
  'updatetime' => 1756283971,
  'route' => '[{"id":82,"fcbl":"50"}]',
  'balance' => '0.00',
  'user_id' => 0,
  'use_start' => '00',
  'use_end' => '23',
  'charging_rule' => 1,
  'overtime_price' => '0.00',
  'contract_price' => '5.00',
  'longitude' => '113.93052',
  'latitude' => '22.53315',
  'yajin' => '0.03',
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 打印orderEnd1结果
log
[ 2025-08-27T17:17:40+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'data' => 1111111111,
)
log
[ 2025-08-27T17:17:40+08:00 ][ log ] 【业务触发】订单(SN:ord2025082709351970903265)自动结束和结算成功: 
log
