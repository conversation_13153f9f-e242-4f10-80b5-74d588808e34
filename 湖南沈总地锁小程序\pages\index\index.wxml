<!--pages/index/index.wxml-->
<view class="container">
  <view class="top">
    <view class="heard" style="padding-top:{{statusBar}}px;">
      <view class="location" style="line-height: {{statusBar}}px;top: {{statusBar}}px;">{{city || '--'}}</view>
      <view class="title" style="line-height: {{statusBar}}px;">首页</view>
    </view>
    <swiper autoplay='true' circular="true" interval='5000' duration='500' class='banner_bg'>
      <swiper-item class="item_image" wx:key="index" wx:for="{{banner}}">
        <image src='{{item.ad_image}}' class='swiper_image' mode="aspectFill"></image>
      </swiper-item>
    </swiper>
  </view>
  <view class="service">
    <view class="title">{{status == 1?'智慧停车一欢迎您的参与':'共享车位一迎接您的到来'}}</view>
    <view class="nav" wx:if="{{status == 1}}">
      <view class="nr" data-type="1" bindtap="getCode">
        <image src="/image/nav_bj1.png" mode="widthFix"></image>
      </view>
      <view class="nr" data-type="2" bindtap="getCode">
        <image src="/image/nav_bj2.png" mode="widthFix"></image>
      </view>
    </view>
    <view class="nav" wx:else>
      <view class="nr" data-type="1" bindtap="getCode">
        <image src="/image/nav_bj3.png" mode="widthFix"></image>
      </view>
      <view class="nr" data-type="2" bindtap="getCode">
        <image src="/image/nav_bj4.png" mode="widthFix"></image>
      </view>
    </view>
  </view>
  <view class="package" bindtap="goStore">
    <view class="nr level">
      <view>查询周边空闲地锁</view>
      <view class="go-btn">立刻前往 ></view>
    </view>
  </view>
  <view class="hint-box-bottom padding-30" wx:if="{{status == 1}}">
    <view class="hint-box-top-title">温馨提示</view>
    <view class="hint-box">
      <view class="hint-box-title">珍爱生命 预防火灾</view>
      <view class="hint-box-content">请您将电动车放在室外充电</view>
      <view class="hint-box-from">——重庆市大渡口区委区政法委</view>
    </view>
  </view>
  <view class="attention" wx:if="{{isToken}}">
    <view class="text">
      <view>友情提示：购买套餐/提前充值</view>
      <view>让停车便捷又实惠</view>
    </view>
    <view class="btn">
      <navigator url="/packageA/package/index" hover-class="none">
        <image src="/image/sy_nav1.png" mode="widthFix" />
      </navigator>
      <navigator url="/packageB/balance/recharge" hover-class="none">
        <image src="/image/sy_nav2.png" mode="widthFix" />
      </navigator>
    </view>
  </view>
  <view class="tabbar-box level">
    <view class="tabbar-one">
      <image src="/image/foot_syb.png" mode="widthFix" />
      <view class="choose">首页</view>
    </view>
    <view class="tabbar-center">重庆大渡口移动公司监制</view>
    <view class="tabbar-one" bind:tap="goMy">
      <image src="/image/foot_wd.png" mode="widthFix" />
      <view>我的</view>
    </view>
  </view>

  <view class="tc tcmobile" wx:if="{{mobileShow}}" catch:tap="handleA">
    <button open-type="getPhoneNumber" catchgetphonenumber="getPhoneNumber">一键获取手机号</button>
  </view>
</view>