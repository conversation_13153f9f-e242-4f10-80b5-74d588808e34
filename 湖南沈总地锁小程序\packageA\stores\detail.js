// packageA/stores/detail.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    allinfo: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('options', options);
    this.setData({
      id: options.id,
    })
    this.getInfo()
  },
  // 获取信息
  getInfo() {
    let that = this
    let params = {
      hospital_id: that.data.id,
      latitude: wx.getStorageSync("lat"),
      longitude: wx.getStorageSync("lon"),
    }
    app.post('hospital/info', params).then(res => {
      const { code, data, msg } = res //接口数据
      if (code == 1) {
        this.setData({
          allinfo: data,
        })
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  },
  goLocation() {
    let that = this
    wx.getLocation({
      type: 'wgs84',
      success: function (res) {
        wx.openLocation({//​使用微信内置地图查看位置。
          latitude: Number(that.data.allinfo.latitude),//要去的纬度-地址
          longitude: Number(that.data.allinfo.longitude),//要去的经度-地址
          name: that.data.allinfo.name,
          address: that.data.allinfo.addr,
          scale: 18,
        })
      }
    })
  },
  getCall(){
    wx.makePhoneCall({
      phoneNumber: this.data.allinfo.kefu,
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})