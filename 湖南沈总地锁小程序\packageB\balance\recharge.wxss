/* packageB/balance/recharge.wxss */
.container {
  padding: 30rpx;
}
.top{
  background: #fff;
  border-radius: 20rpx;
  padding: 10rpx 40rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.top .nr .xx{
  font-size: 42rpx;
  line-height: 60rpx;
  color: #000;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.top .nr .text{
  font-size: 38rpx;
  line-height: 52rpx;
  color: #000;
  font-weight: bold;
}
.top .price{
  width: 170rpx;
  height: 170rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 5rpx solid #E82F3E;
  border-radius: 50%;
}
.top .price .text{
  font-size: 38rpx;
  line-height: 52rpx;
  color: #E82F3E;
  font-weight: bold;
}
.top .price .text text{
  display: inline-block;
  font-size: 38rpx;
  line-height: 52rpx;
  color: #E82F3E;
  font-weight: bold;
  margin-left: 30rpx;
}
.top .price .numb{
  font-size: 46rpx;
  line-height: 52rpx;
  color: #E82F3E;
  font-weight: bold;
}

/* 快捷金额选择 */
.quick-amount-section {
  margin-bottom: 30rpx;
}
.quick-amount-title{
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 28rpx;
}
.quick-amount-title view{
  font-size: 38rpx;
  line-height: 78rpx;
  color: #E82F3E;
  font-weight: bold;
}
.quick-amount-title view:last-child{
  font-size: 56rpx;
  margin-left: 14rpx;
}
.quick-amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.quick-amount-item {
  background: #fff;
  border: 2rpx solid #fff;
  border-radius: 12rpx;
  padding: 44rpx 0 40rpx;
}
.quick-amount-item .text{
  font-size: 38rpx;
  line-height: 54rpx;
  color: #666;
  text-align: center;
}
.quick-amount-item .price{
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  justify-content: center;
  color: #854E13;
}
.quick-amount-item .price view{
  font-size: 40rpx;
  line-height: 48rpx;
}
.quick-amount-item .price text{
  font-size: 64rpx;
  line-height: 64rpx;
  font-weight: bold;
}

.quick-amount-item.selected {
  background: #FFEEEF;
  border-color: #E82F3E;
  color: #fff;
}
.quick-amount-item.selected .price{
  color: #E82F3E;
}

/* 充值说明 */
.recharge-tips {
  margin-bottom: 28rpx;
}

.tips-title {
  font-size: 40rpx;
  line-height: 56rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}


.tip-item {
  font-size: 34rpx;
  line-height: 48rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.pay{
	margin-bottom: 26rpx;
}
.pay .title{
	font-size: 36rpx;
	line-height: 50rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 26rpx;
}
.pay .check{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA55JREFUWEfNV09oFFcY/30zA9lDr6UGPbS0pRaVevCgh1I9t7cqTaBQC2bfm90SIhRqT66ntiAkhE7mvamCh4KBpLd6ag8tHhQUqlCoRYs5KKt49bCBee8rXzoTJpvtZpKt2nfYw+z3/8/v+z5CzZckyUthGL4P4CiAQwD2ENEuYWfmRwAeALgJ4Bfn3JV2u/20jmjaisha+zqAMwA+BtDYir74vwfgewBfK6X+GsbzrwZ0Op3G+Pj4WWb+nIiiipAVANcB3GHmx/KdiF4BsBfAYQCvlrTMnBPR+W63e67T6YhRm95AAxYWFt4Kw3ARwMGKR8Z7fzGO49+HeZQkycEoij4BoCsRuwXg+KBobDIgy7J93vufy/wS0XKv1zs9PT0tOa795ufn9zQajVlmPl7Wiff+aKvV+rMqZIMBSZK8EYbhVVEu4WPmqTiOL9XWOoDQWquY+VtJoxSrc+7ddrt9ryRdN6DI+TUJuygHMKm1Xh5FeclrjJEoXC5q6Va32z1S1sS6Adbar4pqh/f+01E97zdcIgHAFN+lO75cK2D5KUL/h1goOW82myf+C8/7ZWRZtiQ1IRF2zr0tqVgzwFr7HYBTAHqrq6tvbrfg6horhTk2Nna36I4LSqkpEoSLouhJ8XFOKXW6rsCd0FlrZwHMiLN5nr9MxpiPiEh6XnJ/YKs+34nSKk+BE78VrTlBWZalzCygsaKUem1UBXX4rbX3BTGJyJC19kYxXBaVUpN1BIxKY629DGCCma9LCroF6nWUUudGFV6H31p7FkBHJqhEgIXJOTfVarUu1BEwKo0xRhNRuoYDpQHMfEprfXFU4XX4q6D0v0jBiy3Csg2ZeUVr/Vza0Bhzn4j+acM0TSeCIJC2eO5A5L2f3ADFzDyntX6mUGyMmSWiGWZ+6pwbf/HDqH8cM/Oy1vqZjGNjzBIRbR7HxUheX0hkoVRK2To9XZdm6EIiQvpXsiAIJprN5g91FQyjy7LsQ+/94tCVrJKK9aWUiD4bNRK1l9LSgzRN9xPRT5Wza5mZZ+I4fridaKRpupuI5iTnxex/RETHlFJ3qnIGHiayI0ZRtFQ9TJhZFspLWuvbwwwxxrwD4CQRbThM8jw/UV3HSxnbPs0EMYlo7TTz3stRiiAI5Ejdy8yHBeFK4Ts+zfpWKInGF9s5TgVkZM3L8/ybQV5vmYJBIZblNQiCD8IwfI+ZDzHzhvOciB4Q0U3n3K/e+x/rnud/Aw+TCOkgP8ChAAAAAElFTkSuQmCC) no-repeat right center;
	background-size: 36rpx auto;
}
.pay .check.active{
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAA2ZJREFUWEfNmM1PE0EUwN+bbZNWTQ/GpEUCKRGD2i8PmqjQxsaScDUxsUbu1EQP/iUc1MAdDpJIPHjwYE0JC0LiQSglNH4EIgqtxgshFNPdfWaWtiml3e2208S97rw3v3lv3tcgWPzy/lsXQLLFSKMhYOgHAg8geHQ1BDlAyIFGGWS4AKqSdGc+fLOyBTazOAW3bVcC2n0NKYGIQ83IlNcQ0QIiPt9YZbNRmFPMZE2BcsHwHUCcBIB+M2VG/4kgi0CPPWn5vdG6hkCbXq/D4eoZR8REOyC1skQ0ebi3/bRva+uwnt66QJu+6x6H5HyDCNdEwlR0ESwX1MLdvvWPuVr9J4BKMClEuNQRmJJS7sJDtRCthToGdOSmXrljljnhP1gu7H2PVrvvGNBuMDwh+s6YWZnfqa60/Ki8rgJUiqakmYKO/CeKlaNPB+J55nJI22g3tFuF5fcpm2YBnqd0oHwg8pAYTLeqUJBc3LM6P6MD7QbDstUMbAmClxRQhklj51BiqXqyPKN3peUwbvtu9ttt9i+WNrCymCCnUTF6fm0p+yc06CuClGkkXlSKFzEfiowRAC8N4r8qmN/BwQEVpLlKIa6zGwIkMBcITwHDUeE0VTC6FyS7bASj76/RNOZCkU8AcFUoUBWMxcy/grlgZNeInAh+IAJvG7xNQbcOo/dT3EJktFFBKXSdhoN9ks6+JbNeqB2YEoQpEKgQ92TmZ375fGcMoQTAcCZTlwGAgkRxd1qebQglCKbssmYudWMoUTBHLluxEvYKEI3wIlixFGB/OelZjKb615aH/U4onGCAE01G0D4yGnGvyIsc6i+4PD3rS1+FwPA0BPTIeukgqEDxQ4iC4br00tFSceVQSPckUDJFsidFtLuV4sqBdvzhUSbhVFNu69AilehBd1p++V80aECQ3ahu0PihfwaGYhJj7zpkAEO1qqYNd68t6O3zsSY/HwxPkODB0OyASDTprtfk6xHj9Tqcrt4UINwwUyTkPx8YjcYg0WFsBN3UoFhWwHOLU3K+7pilrIzSFSiv13HK1TMu+k7xO3Ng9bGh2tR69CF7Bu3O+gRZlbQn5Whq5E7T9yEuyAfJAb8aRwZjVsclnoE1gBef09IrIQ9WtSfhDbtks8WYBoNGT3oag0VVUZK8+FqJyH+QueHRKFNdHAAAAABJRU5ErkJggg==) no-repeat right center;
	background-size: 36rpx auto;
}
.pay .check image{
	width: 41rpx;
	height: 36rpx;
}
.pay .check view{
	font-size: 34rpx;
	line-height: 48rpx;
	color: #333;
	margin-left: 20rpx;
}
/* 确认充值按钮 */
.recharge-btn {
  width: 100%;
  background: #F93F4E;
  border: none;
  border-radius: 50rpx;
  font-size: 64rpx;
  line-height: 114rpx;
  color: #fff;
  font-weight: bold;
}

.recharge-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.recharge-btn::after {
  border: none;
}
