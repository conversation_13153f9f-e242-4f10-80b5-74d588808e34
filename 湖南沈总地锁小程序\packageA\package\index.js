// packageA/package/index.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		store_name: '',// 选择的物业
		store_id: '',// 选择的物业ID
		isCheck: true,// 支付方式
		isAgreement: false,// 协议
		Agreement: '',// 协议信息

		list: [],// 套餐列表
		setmeal_id: '',// 套餐ID
		instruction: '',// 购买套餐说明

		isShow: true,// 弹窗

		safeBottom: app.globalData.safeBottom,
	},



	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		// this.getHospital()
		this.getInstruction()
	},

	// 获取默认门店
	getHospital() {
		let that = this;
		let params = {
			latitude: wx.getStorageSync("lat"),
			longitude: wx.getStorageSync("lon"),
			hospital_id: '',
		}
		app.post('Hospital/info', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					store_id: data.id,
					store_name: data.name,
				});
				that.getSetmeal()
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 购买套餐说明
	getInstruction() {
		let that = this;
		let params = {
			id: 6,
		}
		app.post('Ajax/getArticleInfo', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					instruction: data.content
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 选择物业
	getStore() {
		wx.navigateTo({
			url: 'stores',
		})
	},

	// 获取套餐
	getSetmeal() {
		let that = this;
		let params = {
			hospital_id: that.data.store_id,
			token: wx.getStorageSync("token"),
		}
		app.post('Setmeal/lists', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					list: data
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 选择套餐
	getPackage(e) {
		this.setData({
			setmeal_id: e.currentTarget.dataset.id
		})
	},

	// 支付方式
	getCheck() {
		this.setData({
			isCheck: !this.data.isCheck
		})
	},


	getAgreement() {
		let that = this
		let params = {
			id: '7'
		}
		app.post('Ajax/getArticleInfo', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					isShow: false,
					Agreement: data.content,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 关闭弹层
	getclose() {
		this.setData({
			isShow: true,
		});
	},

	// 提交
	getSubmit() {
		let that = this
		if (!that.data.store_id) {
			wx.showToast({
				title: '请选择物业',
				icon: 'none',
				duration: 2000
			})
			return
		}
		if (!that.data.setmeal_id) {
			wx.showToast({
				title: '请选择套餐',
				icon: 'none',
				duration: 2000
			})
			return
		}
		if (!that.data.isCheck) {
			wx.showToast({
				title: '请选择支付方式',
				icon: 'none',
				duration: 2000
			})
			return
		}
		wx.showLoading({
			title: '支付中',
		})
		let params = {
			setmeal_id: that.data.setmeal_id,
			hospital_id: that.data.store_id,
			token: wx.getStorageSync("token"),
		}
		app.post('Setmeal/pay', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				wx.requestPayment({
					timeStamp: data.timeStamp,
					nonceStr: data.nonceStr,
					package: data.package,
					signType: data.signType,
					paySign: data.paySign,
					success() {
						wx.hideLoading()
						wx.showToast({
							title: "支付成功",
							icon: "none"
						});
						setTimeout(function () {
							wx.switchTab({
								url: '/pages/index/index'
							})
						}, 2000);
					},
					fail(res) {
						wx.hideLoading()
						wx.showToast({
							title: "支付失败",
							icon: "none"
						});
						setTimeout(function () {
							wx.switchTab({
								url: '/pages/index/index'
							})
						}, 2000);
					}
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})