/* pages/index/my.wxss */
.container{
	padding: 0 30rpx;
}
.top{
	margin-bottom: 40rpx;
}
.top .userinfor{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 0 12rpx;
	margin-bottom: 60rpx;
}
.top .userinfor .pic{
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	overflow: hidden;
}
.top .userinfor .pic image{
	width: 100%;
	height: 100%;
}
.top .userinfor .sqdl{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #333;
	margin-left: 37rpx;
	font-weight: bold;
}
.top .userinfor .name{
	margin-left: 37rpx;
}
.top .userinfor .name view{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 6rpx;
}
.top .userinfor .name text{
	font-size: 24rpx;
	line-height: 34rpx;
	color: #999;
	display: block;
}
.top .userinfor .name button{
	font-size: 24rpx;
	line-height: 34rpx;
	color: #999;
	display: block;
	background: none;
	padding: 0;
}
.top .userinfor .name button::after{
	border: none;
}
.page .title{
  font-size: 40rpx;
  line-height: 56rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.page .nr{
  font-size: 34rpx;
  line-height: 48rpx;
  color: #333;
}
.nav{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 30rpx;
}
.nav .item{
  width: 334rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 20rpx;
}
.nav .item .pic{
  width: 55rpx;
}
.nav .item .pic image{
  width: 100%;
  height: auto;
}
.nav .item .xx{
  font-size: 36rpx;
  line-height: 110rpx;
  color: #333;
  font-weight: bold;
  margin-left: 30rpx;
}
.tel{
  display: flex;
  flex-wrap: wrap;
  margin-top: 30rpx;
}
.tel .title{
  font-size: 40rpx;
  line-height: 40rpx;
  color: #333;
  letter-spacing: 12rpx;
  writing-mode: vertical-lr;
  font-weight: bold;
}
.tel .list{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 30rpx;
}
.tel .list view{
  font-size: 34rpx;
  line-height: 48rpx;
  color: #000;
  font-weight: bold;
}


/* 底部tab */
.tabbar-box{
	padding: 0 115rpx;
	width: 100%;
	height: 100rpx;
	box-shadow: -1rpx 3rpx 27rpx 1rpx rgba(198,194,194,0.24);
	background: #FFFFFF;
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 99999;
}
.tabbar-one{
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 24rpx;
	color: #999999;
	text-align: center;
}
.tabbar-one image{
	width: 44rpx;
	height: 44rpx;
	margin-bottom: -8rpx;
}
.tabbar-one .choose{
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 24rpx;
	color: #2298F9;
}
.tabbar-center{
	font-family: PingFang SC, PingFang SC;
	font-weight: 800;
	font-size: 32rpx;
	color: #000000;
}