<?php
// [新增文件] application/common/library/workerman_events/LockEvents.php

namespace app\common\library\workerman_events;

//use app\api\controller\order\Order as ord;
use app\common\logic\Order as OrderLogic;
use think\Db;
use think\Log;

/**
 * 地锁MQTT事件处理类
 */
class LockEvents
{
    /**
     * 处理地锁上线/离线消息
     * @param string $topic   主题
     * @param array  $payload 消息内容 (已json_decode)
     */
    public static function handleStatusMessage($topic, $payload)
    {

        // 从主题中解析出地锁ID
        // dz/pi/mstatus/[地锁ID]
        $parts = explode('/', $topic);
        $sn = end($parts);

        if (!$sn) {
            Log::error("无法从主题 '{$topic}' 中解析出地锁SN");
            return;
        }

        // 从消息体中获取在线状态
        $isOnline = isset($payload['MS']) ? intval($payload['MS']) : null;
        if ($isOnline === null) {
            Log::error("地锁 '{$sn}' 的状态消息中缺少 'MS' 字段");
            return;
        }

        // 获取信号强度
        $signalStrength = isset($payload['SS']) ? intval($payload['SS']) : 0;

        trace("收到地锁 '{$sn}' 的状态更新: " . ($isOnline ? '上线' : '离线') . ", 信号强度: " . $signalStrength);

        try {
            // 查找设备
            $equipment = Db::name('equipment')->where('sn', $sn)->find();

            if (!$equipment) {
                trace("未在数据库中找到SN为 '{$sn}' 的地锁设备");
                return;
            }

            // 准备更新的数据
            $updateData = [
                'is_online'       => $isOnline,
                'signal_strength' => $signalStrength,
                'updatetime'      => time(),
                'last_status_updatetime'=>time(),//最后状态上报时间
            ];

            // 如果是上线，则更新最后上线时间
            if ($isOnline) {
                $updateData['last_online_time'] = time();
            }

            Db::name('equipment')->where('id', $equipment['id'])->update($updateData);

            trace("成功更新地锁 '{$sn}' 的数据库状态为: " . ($isOnline ? '在线' : '离线'));

        } catch (\Exception $e) {
            Log::error("更新地锁 '{$sn}' 状态时发生数据库错误: " . $e->getMessage());
        }
    }




    /**
     * 处理地锁的通用状态上报 (`dz/pi/getstatus/[地锁ID]`)
     * 这个方法负责解析地锁上报的详细状态，并更新数据库。
     *
     * @param string $topic   完整的主题, 例如 "dz/pi/getstatus/12345"
     * @param array  $payload 消息内容 (已通过 json_decode 转换的关联数组)
     */
    public static function handleGetStatusMessage($topic, $payload)
    {
        trace('进入handleGetStatusMessage');
        // --- 从主题中解析出地锁的唯一标识 (SN) ---
        $parts = explode('/', $topic);
        $sn = end($parts); // SN码通常是主题的最后一部分

        // 安全检查：如果无法解析出SN，则记录错误并中止
        if (!$sn) {
            Log::error("【状态上报】无法从主题 '{$topic}' 中解析出地锁SN");
            return;
        }

        trace("【状态上报】开始处理地锁 '{$sn}' 的通用状态...");

        // --- 1. 从消息负载(payload)中安全地清洗和提取数据 ---
        // 我们使用三元运算符来确保即使消息中缺少某个字段，程序也不会报错，而是将其值设为 null。

        /** @var int|null $lockStatus 上锁状态 (LS): 0=开锁, 1=上锁 */
        $lockStatus     = isset($payload['LS']) ? intval($payload['LS']) : null;

//        /** @var int|null $carStatus 车辆状态 (CS): 0=无车, 1=有车 */
//        $carStatus      = isset($payload['CS']) ? intval($payload['CS']) : null;
        // 使用位运算(& 1)来精确获取 bit0 的值，这才是真正的车辆状态
        $carStatus = isset($payload['CS']) ? (intval($payload['CS']) & 1) : null;

        /** @var float|null $voltage 电池电压 (BS), 单位V */
        $voltage        = isset($payload['BS']) ? floatval($payload['BS']) : null;

        /** @var int|null $errorCode 异常标志 (NG): 0=无错误, >0=有错误 */
        $errorCode      = isset($payload['NG']) ? intval($payload['NG']) : null;

        /** @var string|null $messageType 消息类型 (MT): 用于区分上报原因, 例如 "2"代表车辆状态变化 */
        $messageType    = isset($payload['MT']) ? strval($payload['MT']) : null;

        /** @var int|null $signalStrength 信号强度 (RSSI), 单位db */
        $signalStrength = isset($payload['RSSI']) ? intval($payload['RSSI']) : null;


        // --- 2. 核心业务逻辑：更新设备实时状态快照表 (fa_equipment) ---
        try {
            // 根据SN码查找对应的设备记录
            $equipment = Db::name('equipment')->where('sn', $sn)->find();

            // 如果设备不存在于我们的数据库中，记录警告并中止
            if (!$equipment) {
                trace("【状态上报】未在数据库中找到SN为 '{$sn}' 的地锁设备");
                return;
            }

            // 准备要更新到数据库的数据数组
            $updateData = [
                'lock_status'            => $lockStatus,
                'car_status'             => $carStatus,
                'voltage'                => $voltage,
                'error_code'             => $errorCode,
                'signal_strength'        => $signalStrength,
                'is_online'              => 1, // 设备能上报状态，必然是在线状态
                'updatetime'             => time(), // 更新记录的修改时间
                'last_status_updatetime' => time(), // 专门记录最后一次状态上报的时间，用于监控
            ];

            // 使用 array_filter 过滤掉值为 null 的字段。
            // 这样做的好处是：如果上报的消息体不完整，我们只更新收到的字段，而不会用 null 去覆盖数据库中已有的值。
            $updateData = array_filter($updateData, function($value) {
                return $value !== null;
            });

            // 只有当有有效数据需要更新时，才执行数据库操作
            if (!empty($updateData)) {
                Db::name('equipment')->where('id', $equipment['id'])->update($updateData);
                trace("【状态上报】成功更新地锁 '{$sn}' 的快照状态到数据库");
            }

        } catch (\Exception $e) {
            Log::error("【状态上报】更新地锁 '{$sn}' 快照状态时发生数据库错误: ". $e->getMessage());
            // 注意：即使快照更新失败，我们仍然会尝试继续记录日志，以保留原始数据。
        }

        // --- 3. 核心业务逻辑：将每一条上报都记录到历史日志表 (fa_equipment_log) ---
        try {
            // 准备要插入到日志表的数据数组
            $logData = [
                'sn'              => $sn,
                'message_type'    => $messageType,
                'lock_status'     => $lockStatus,
                'car_status'      => $carStatus,
                'voltage'         => $voltage,
                'signal_strength' => $signalStrength,
                'error_code'      => $errorCode,
                'raw_payload'     => json_encode($payload, JSON_UNESCAPED_UNICODE), // 存储原始消息，用于追溯
                'createtime'      => time(),
            ];

            // 同样，过滤掉值为 null 的字段
            $logData = array_filter($logData, function($value) {
                return $value !== null;
            });

            Db::name('equipment_log')->insert($logData);
            trace("【状态上报】成功记录地锁 '{$sn}' 的状态到日志表");

        } catch (\Exception $e) {
            Log::error("【状态上报】记录地锁 '{$sn}' 状态日志时发生数据库错误: ". $e->getMessage());
        }


        // --- 4. 根据状态变化，触发后续业务逻辑 ---
        trace("【业务触发】检查 '{$sn}' 的状态变化...");
        trace('打印设备信息');
        trace($equipment);
        //这里区分一下设备类型,是二轮地锁还是四轮地锁
        if($equipment['hardware_type']==1){//2轮
            // **场景1: 关锁成功 (地锁从“开”变为“关”)**
            // 触发条件: 消息类型为升降锁变化(MT=4), 且数据库中原状态为开(0), 新状态为关(1)
            if ($messageType == 4 && $equipment['lock_status'] == 0 && $lockStatus === 1) {
                trace("【业务触发】检测到地锁 '{$sn}' 关锁成功，开始处理订单...");
                // 在这里调用订单处理逻辑，例如：
                // OrderLogic::startOrder($sn);
                //从订单表中查询最新一条设备信息
                $order = Db::name('order')
                    ->where(['equipment_id' => $equipment['id'],
                        'status' => 1,
                        'use_status' => 1
                    ])->order('id desc')
                    ->find();
                if ($order) {
                    $order_update = array(
                        'createtime' => time(),
                        'use_status' => 2,
                    );
                    Db::name('order')->where(['id' => $order['id']])->update($order_update);
                    //把设备改为使用中
                    Db::name('equipment')->where(['id' => $equipment['id']])->update(['use_status' => 2]);
                }


                // 暂时只打印日志
            }

            // **场景2: 开锁成功 (地锁从“关”变为“开”)**
            // 触发条件: 消息类型为升降锁变化(MT=4), 且数据库中原状态为关(1), 新状态为开(0)
            if ($messageType == 4 && $equipment['lock_status'] == 1 && $lockStatus === 0) {
                trace("【业务触发】检测到地锁 '{$sn}' 开锁成功，结束处理订单...");
                // 在这里调用订单处理逻辑，例如：
                // OrderLogic::endOrder($sn);
                //从订单表中查询最新一条设备信息
                $order = Db::name('order')
                    ->where(['equipment_id' => $equipment['id'],
                        'status' => 1,
                        'use_status' => 2
                    ])->order('id desc')
                    ->find();
                trace('打印order');
                trace($order);
                if ($order) {
                    trace('实例化OrderLogic1');
                    // 实例化我们干净的 OrderLogic 类
                    $orderLogic = new OrderLogic();
                    trace('实例化OrderLogic2');
                    // 调用纯净的业务逻辑方法
                    $result = $orderLogic->orderEnd($order);
                    trace('实例化OrderLogic3');
                    // 记录逻辑处理结果
                    if ($result['success']) {
                        Log::error("【业务触发】订单(SN:{$order['sn']})结束逻辑处理成功");
                        // 在这里可以继续做发送模板消息等操作
                    } else {
                        Log::error("【业务触发】订单(SN:{$order['sn']})结束逻辑处理失败: " . $result['msg']);
                    }
                }
            }

            // **场景3: 车辆状态变化 (无车 -> 有车)**
            if ($messageType == 2 && $equipment['car_status'] == 0 && $carStatus === 1) {
                trace("【业务触发】检测到地锁 '{$sn}' 上方有车停入...");
                // 根据您的业务，这里也可能触发订单或记录事件
            }

            // **场景4: 车辆状态变化 (有车 -> 无车)**
            if ($messageType == 2 && $equipment['car_status'] == 1 && $carStatus === 0) {
                trace("【业务触发】检测到地锁 '{$sn}' 上方车辆离开...");
            }

            // **场景5: 异常报警**
            // 触发条件: error_code 不为0，并且和之前的值不同
            if ($errorCode !== null && $errorCode > 0 && $equipment['error_code'] != $errorCode) {
                Log::warning("【业务触发】地锁 '{$sn}' 上报异常报警，错误码: {$errorCode}");
                // 在这里可以调用报警通知逻辑，比如发短信给运维人员
                // AlarmLogic::notifyAdmin($sn, $errorCode);
            }
        }
        trace('打印四轮地锁有车无车的状态');
        trace($carStatus);
        if($equipment['hardware_type']==2){//4轮
            // **场景4: 车辆状态变化 (有车 -> 无车)**
            if ($messageType == 2 && $equipment['car_status'] == 1 && $carStatus == 0) {
                trace("【业务触发】检测到地锁 '{$sn}' 车辆离开，尝试自动结束订单。");
                // 找到与该设备关联的、正在使用中的订单
                $order = Db::name('order')
                    ->where('equipment_id', $equipment['id'])
                    ->where('status', 1) // 1=使用中
                    ->find();
                trace('打印四轮地锁order信息');
                trace($order);

                if ($order) {
//                    try {
                        // 实例化我们的订单逻辑类
                        $orderLogic = new OrderLogic();

                        // 调用核心的订单结束和结算方法
                        $result = $orderLogic->orderEnd1($order);
                        trace('打印orderEnd1结果');
                        trace($result);
                        if ($result['success'] == true) {
                            trace("【业务触发】订单(SN:{$order['sn']})自动结束和结算成功: " . $result['msg']);
                        } else {
                            trace("【业务触发】订单(SN:{$order['sn']})自动结束和结算失败: " . $result['msg']);
                        }
//                    } catch (\Exception $e) {
//                        trace("【业务触发】处理订单(SN:{$order['sn']})时发生严重异常: " . $e->getMessage().$e->getLine());
//                    }
                } else {
                    trace("【业务触发】地锁 '{$sn}' 车辆离开，但未找到关联的使用中订单。");
                }


            }
        }






    }



    /**
     * 处理地锁的命令确认响应 (`dz/pi/setack/[地锁ID]`)
     * 当后台下发指令后，地锁会快速回复此消息表示“已收到指令”。
     *
     * @param string $topic   完整的主题, 例如 "dz/pi/setack/12345"
     * @param array  $payload 消息内容 (已通过 json_decode 转换的关联数组)
     */
    public static function handleSetAckMessage($topic, $payload)
    {
        // --- 1. 从消息负载(payload)中安全地清洗和提取数据 ---

        /** @var string|null $packId 命令包ID (PACKID): 用于唯一关联某条命令 */
        $packId = isset($payload['PACKID']) ? strval($payload['PACKID']) : null;

        /** @var string|null $status 响应状态 (STATUS): 十六进制字符串，如 "81" */
        $status = isset($payload['STATUS']) ? strval($payload['STATUS']) : null;

        // 安全检查：如果缺少关键的PACKID，则无法处理
        if (!$packId) {
            Log::error("【命令响应】收到的响应消息缺少 'PACKID' 字段: " . json_encode($payload));
            return;
        }

        trace("【命令响应】收到针对命令包 '{$packId}' 的ACK确认");

        try {
            // --- 2. 核心业务逻辑：更新命令日志表 (fa_equipment_command_log) ---

            // 根据pack_id查找我们之前发送的命令记录
            $commandLog = Db::name('equipment_command_log')->where('pack_id', $packId)->find();

            if (!$commandLog) {
                trace("【命令响应】在日志表中未找到PACKID为 '{$packId}' 的命令记录");
                return;
            }

            // 准备更新的数据
            $updateData = [
                'status'        => 'acked', // 将命令状态从 'pending' (待处理) 更新为 'acked' (已确认)
                'ack_payload'   => json_encode($payload, JSON_UNESCAPED_UNICODE), // 存储完整的响应内容
                'ack_time'      => time(),   // 记录响应时间
            ];

            Db::name('equipment_command_log')->where('id', $commandLog['id'])->update($updateData);

            trace("【命令响应】成功更新命令 '{$packId}' 的状态为 'acked'");

        } catch (\Exception $e) {
            Log::error("【命令响应】更新命令 '{$packId}' 日志时发生数据库错误: " . $e->getMessage());
        }
    }

}