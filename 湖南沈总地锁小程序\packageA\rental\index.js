// packageA/rental/index.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    kpl: '',
    hospital: '',
    deposit_amount: '',
    info: '',

    isPay: false,
    order_id: '', // 订单ID
    timer: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      kpl: options.kpl,
    })
    if (options.hospital) {
      this.setData({
        hospital: options.hospital,
        deposit_amount: options.deposit_amount,
        isPay: false,
      })
    } else {
      this.setData({
        isPay: true,
      })
    }
    this.getInfo()
  },

  getInfo() {
    let that = this
    let params = {
      kpl: that.data.kpl
    }
    app.post('ajax/lockinfo', params).then(res => {
      const { code, data, msg } = res //接口数据
      if (code == 1) {
        that.setData({
          info: data,
        })
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
    })
  },

  getPay() {
    let that = this
    if (that.data.isPay) {
      // 降锁
      let that = this
      let params = {
        kpl: that.data.kpl
      }
      app.post('Order/index', params).then(res => {
        const { code, data, msg } = res //接口数据
        if (code == 1) {
          wx.showLoading({
            title: '正在锁车中...',
            mask: false,
          })
          that.setData({
            order_id: data,
          })
          let timer = setInterval(() => {
            that.Order();
          }, 1000)
          this.setData({
            timer,
          })
        } else {
          wx.showToast({
            title: msg,
            icon: 'none',
            duration: 2000
          })
        }
      }).catch((err) => {

      })
    } else {
      wx.showLoading({
        title: '支付中...',
        mask: true,
      })
      let params = {
        hospital_id: that.data.hospital
      }
      app.post('order/depositRecharge', params).then(res => {
        const { code, data, msg } = res //接口数据
        if (code == 1) {
          that.callWechatPay(data)
        } else {
          wx.showToast({
            title: msg,
            icon: 'none',
            duration: 2000
          })
        }
      }).catch((err) => {
      })
    }
  },
  callWechatPay(payData) {
    let that = this
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success() {
        that.getPaystatus()
      },
      fail(res) {
        wx.hideLoading()
        console.log('支付失败', res)
        if (res.errMsg.includes('cancel')) {
          wx.showToast({
            title: "支付已取消",
            icon: "none",
            duration: 2000
          })
        } else {
          wx.showToast({
            title: "支付失败",
            icon: "none",
            duration: 2000
          })
        }
      }
    })
  },
  getPaystatus() {
    let that = this
    let params = {}
    app.post('user/is_deposit', params).then(res => {
      const { code, data, msg } = res //接口数据
      if (code == 1) {
        wx.hideLoading()
        wx.showToast({
          title: "支付成功",
          icon: "none",
          duration: 2000
        })
        that.setData({
          isPay: true,
        })
      } else {
        setTimeout(function () {
          that.getPaystatus()
        }, 3000);
      }
    }).catch((err) => {
    })
  },


  // 4-订单开始使用
  Order() {
    console.log('订单开始')
    let that = this
    let params = {
      order_id: that.data.order_id
    }
    app.post('Order/orderUse', params).then(res => {
      console.log('new新的');
      const { code, data, msg } = res //接口数据
      if (code == 1) {
        console.log('开锁了');
        wx.showToast({
          title: '锁车成功',
          icon: 'none',
          duration: 2000
        })
        clearInterval(this.data.timer)
        setTimeout(() => {
          wx.navigateBack();
        }, 1000)
      } else {
        // wx.showToast({
        // 	title: msg,
        // 	icon: 'none',
        // 	duration: 2000
        // })
      }
    }).catch((err) => {

    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
		clearInterval(this.data.timer)
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})