{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": false, "es6": false, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "editorSetting": {"tabIndent": "tab", "tabSize": 2}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "plugin": {"list": []}, "game": {"currentL": -1, "list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"id": -1, "name": "分类", "pathName": "pages/fl/index", "query": "", "scene": null}, {"id": -1, "name": "登录", "pathName": "pages/landing/index", "query": "", "scene": null}, {"id": -1, "name": "手机绑定", "pathName": "pages/landing/sjbd", "query": "", "scene": null}, {"id": -1, "name": "忘记密码", "pathName": "pages/landing/wjmm", "query": "", "scene": null}, {"id": -1, "name": "注册", "pathName": "pages/landing/zc", "query": "", "scene": null}, {"id": -1, "name": "搜索", "pathName": "pages/index/search", "query": "", "scene": null}, {"id": -1, "name": "我的", "pathName": "pages/wd/index", "query": "", "scene": null}, {"id": -1, "name": "关于我们", "pathName": "pages/wd/gywm", "query": "", "scene": null}, {"id": -1, "name": "我的客服", "pathName": "pages/wd/wdkf", "query": "", "scene": null}, {"id": -1, "name": "公告详情", "pathName": "pages/index/ggxq", "query": "", "scene": null}, {"id": -1, "name": "代金券", "pathName": "pages/djj/index", "query": "", "scene": null}, {"id": -1, "name": "购物车", "pathName": "pages/gwc/index", "query": "", "scene": null}, {"id": -1, "name": "公告列表", "pathName": "pages/index/gglb", "query": "", "scene": null}, {"id": -1, "name": "我的资料", "pathName": "pages/wd/wdzl", "query": "", "scene": null}, {"id": -1, "name": "地址管理", "pathName": "pages/wd/dzgl", "query": "", "scene": null}, {"id": -1, "name": "编辑地址", "pathName": "pages/wd/bjdz", "query": "", "scene": null}, {"id": -1, "name": "添加地址", "pathName": "pages/wd/tjdz", "query": "", "scene": null}, {"id": -1, "name": "我的订单", "pathName": "pages/wd/wddd", "query": "", "scene": null}, {"id": -1, "name": "提交订单", "pathName": "pages/gwc/tjdd", "query": "", "scene": null}, {"id": -1, "name": "产品详情", "pathName": "pages/index/cpxq", "query": "", "scene": null}, {"id": -1, "name": "产品列表", "pathName": "pages/index/cplb", "query": "", "scene": null}, {"id": -1, "name": "注册店铺", "pathName": "pages/landing/zcdp", "scene": null}]}}, "packOptions": {"ignore": [], "include": []}, "appid": "wx25b5cea858956c3c", "libVersion": "3.8.12"}