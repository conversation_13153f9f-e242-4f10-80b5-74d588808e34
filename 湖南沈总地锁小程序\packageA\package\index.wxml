<!--packageA/package/index.wxml-->
<view class="container" style="padding-bottom: {{safeBottom}}px;">
	<view class="package">
		<view class="store" bindtap="getStore">
			<view class="title">选择门店：</view>
			<view class="nr">{{store_name?store_name:'请选择门店'}}</view>
		</view>
		<view class="state" wx:if="{{list.length > 0}}">
			<view class="title">套餐标准</view>
			<view class="list">
				<view class="nr {{setmeal_id == item.id?'active':''}}" bindtap="getPackage" data-id="{{item.id}}" wx:for="{{list}}">
					<view class="name">{{item.name}}</view>
					<view class="price">
						<view>￥</view>
						<text>{{item.price}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="zwsj" wx:else>
			<image src="/image/icon_wsj.png" mode="widthFix"></image>
			<view>暂无套餐</view>
		</view>
		<view class="instruction">
			<view class="title">购买套餐协议</view>
			<view class="xx">
        <view>1.购买套餐为微信线上支付，即时到账，不支持线上退款,可以线下办理退款。</view>
        <view>2.购买成功后，金额会自动进入您的账户，您可随时查看。 </view>
				<!-- <rich-text nodes="{{instruction}}"></rich-text> -->
			</view>
		</view>
		<view class="pay">
			<view class="title">支付方式</view>
			<view class="check {{isCheck?'active':''}}" bindtap="getCheck">
				<image src="/image/icon_wx.png"></image>
				<view>微信支付</view>
			</view>
		</view>
		<view class="btn">
			<view class="submit" bindtap="getSubmit">确认协议并支付</view>
		</view>


		<view class="tc" hidden="{{isShow}}">
			<view class="tcxy">
				<view class="close" bindtap="getclose">
					<image src="/image/icon_closeb.png"></image>
				</view>
				<view class="title">购买协议</view>
				<view class="desc">
					<rich-text nodes="{{Agreement}}"></rich-text>
				</view>
			</view>
		</view>
	</view>
</view>