-- 订单抵扣功能数据库变更脚本
-- 执行前请备份数据库

-- 1. 为 fa_order 表添加抵扣相关字段
ALTER TABLE `fa_order` ADD COLUMN `deposit_deducted` decimal(10,2) DEFAULT '0.00' COMMENT '押金抵扣金额';
ALTER TABLE `fa_order` ADD COLUMN `balance_deducted` decimal(10,2) DEFAULT '0.00' COMMENT '余额抵扣金额';
ALTER TABLE `fa_order` ADD COLUMN `remaining_amount` decimal(10,2) DEFAULT '0.00' COMMENT '剩余需支付金额';
ALTER TABLE `fa_order` ADD COLUMN `payment_detail` text COMMENT '支付明细JSON';
ALTER TABLE `fa_order` ADD COLUMN `setmeal_log_id` int(10) DEFAULT '0' COMMENT '使用的套餐记录ID';

-- 2. 为 fa_pay 表添加抵扣相关字段
ALTER TABLE `fa_pay` ADD COLUMN `deduction_type` tinyint(1) DEFAULT NULL COMMENT '抵扣类型:1=押金抵扣,2=余额抵扣,3=微信支付,4=押金退款,5=套餐抵扣';
ALTER TABLE `fa_pay` ADD COLUMN `parent_pay_id` int(10) DEFAULT '0' COMMENT '关联支付ID(押金抵扣时关联原押金支付记录)';
ALTER TABLE `fa_pay` ADD COLUMN `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额';

-- 3. 扩展 fa_pay 表的 types 字段枚举值
ALTER TABLE `fa_pay` MODIFY COLUMN `types` enum('1','2','3','4','5','6','7') DEFAULT NULL COMMENT '支付类型:1=押金充值,2=租用费用,3=购买套餐,4=余额充值,5=套餐抵扣,6=押金抵扣,7=余额抵扣';

-- 4. 为新增字段添加索引（可选，提升查询性能）
ALTER TABLE `fa_order` ADD INDEX `idx_deposit_deducted` (`deposit_deducted`);
ALTER TABLE `fa_order` ADD INDEX `idx_balance_deducted` (`balance_deducted`);
ALTER TABLE `fa_order` ADD INDEX `idx_remaining_amount` (`remaining_amount`);
ALTER TABLE `fa_order` ADD INDEX `idx_setmeal_log_id` (`setmeal_log_id`);

ALTER TABLE `fa_pay` ADD INDEX `idx_deduction_type` (`deduction_type`);
ALTER TABLE `fa_pay` ADD INDEX `idx_parent_pay_id` (`parent_pay_id`);

-- 执行完成后，请检查表结构是否正确
-- 可以使用以下命令查看表结构：
-- DESCRIBE fa_order;
-- DESCRIBE fa_pay;
