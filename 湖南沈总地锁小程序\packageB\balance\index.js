// packageB/balance/index.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    customBar: app.globalData.customBar,
    userBalance: 0, // 用户余额
    balanceList: [], // 余额明细列表
    page: 1,
    limit: 20,
    hasMore: true,
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getBalanceList()
  },

  /**
   * 获取用户余额
   */
  getUserBalance() {
    let that = this
    let params = {}
    app.post('User/getUserInfo', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        that.setData({
          userBalance: data.money || '0.00'
        })
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      console.log('获取用户余额失败', err)
    })
  },

  /**
   * 获取余额明细列表
   */
  getBalanceList() {
    if (this.data.loading || !this.data.hasMore) return
    
    let that = this
    that.setData({ loading: true })
    
    let params = {
      page: that.data.page,
      page_size: that.data.limit
    }
    
    app.post('User/getMoneyLog', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        let newList = data.data || []
        let balanceList = that.data.page === 1 ? newList : that.data.balanceList.concat(newList)
        
        that.setData({
          balanceList: balanceList,
          hasMore: newList.length >= that.data.limit,
          page: that.data.page + 1,
          loading: false
        })
      } else {
        that.setData({ loading: false })
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      that.setData({ loading: false })
      console.log('获取余额明细失败', err)
    })
  },





  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从充值页面返回时刷新余额
    this.getUserBalance()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true,
      balanceList: []
    })
    this.getUserBalance()
    this.getBalanceList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.getBalanceList()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 格式化时间 - 在模板中使用
   */
  formatTime: function(timestamp) {
    const date = new Date(timestamp * 1000)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  /**
   * 获取交易类型文字 - 在模板中使用
   */
  getTypeText: function(type) {
    const typeMap = {
      1: '充值金额',
      2: '提现',
      3: '购买套餐',
      4: '退款',
      5: '设备租赁'
    }
    return typeMap[type] || '其他'
  },

  /**
   * 获取交易类型图标 - 在模板中使用
   */
  getTypeIcon: function(type) {
    // 根据类型返回对应图标
    return type == 1 ? '/image/icon_recharge_red.png' : '/image/icon_rent_orange.png'
  }
})
