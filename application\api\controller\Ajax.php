<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use app\api\controller\Cabinet as cab;
use think\Db;

/**
 * 会员接口
 */
class Ajax extends Api {

    protected $noNeedLogin = ['getConfig', 'getArticleInfo','getLaunchInfo'];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }


    /**
     * 根据设备SN码获取小程序启动信息（如跳转页面）
     * @ApiMethod (GET)
     * @ApiParams (name="sn", type="string", required=true, description="设备SN码")
     */
    public function getLaunchInfo()
    {
        $sn = $this->request->param('kpl');

        $type=2;
        if($sn){
            // 1. 根据SN码查询设备，并关联查询其所属客户
//            $equipment = Db::name('equipment')
//                ->alias('e')
//                ->join('disuo_kehu k', 'e.kehu_id = k.id', 'LEFT')
//                ->where('e.sn', $sn)
//                ->field('e.id, e.sn, k.wx_page_path')
//                ->find();
//
//            if (!$equipment) {
//                $this->error('设备未注册或不存在');
//            }

            $equipment = Db::name('equipment')->where(['sn'=>$sn])->find();
            if($equipment['kehu_id']==1){
                $type=1;
            }else{
                $type=2;
            }

        }


        // 2. 决定跳转路径
//        $default_page = 'pages/index/index'; // 您的默认首页
//        $page_path = (!empty($equipment['wx_page_path'])) ? $equipment['wx_page_path'] : $default_page;

        // 3. 返回数据给小程序
        $this->success('获取成功', [
            'type' => $type
        ]);
    }

    /**
     * 根据床编码，查询床信息,判断是否可用
     * kpl 陪护床编码
     */
    public function getEquipmentInfo() {
        $kpl = input('kpl', null);
        $return = $this->useVerification($kpl);
        if ($return['success']) {
            $this->success($return['msg'], $return['data']);
        } else {
           if(isset($return['code'])){
               $this->error($return['msg'],$return['data'],$return['code']);
           }

            $this->error($return['msg']);
        }
    }

    public function useVerification($kpl) {

        $return = [
            'success' => false,
            'msg' => '',
            'data' => [],
        ];

        if ($kpl) {
            $equipment = db('equipment')->where(['sn' => $kpl])->find();
            if ($equipment) {
                $hospital = db('hospital')->where(['id' => $equipment['hospitals_id']])->find();
                $hospital['use_start'] = (int) $hospital['use_start'];
                $hospital['use_end'] = (int) $hospital['use_end'];


                // --- 步骤 2.1 开始：押金前置检查 ---  判断设备所属客户,重庆客户不检查押金,其他客户都要检查押金

                if($equipment['kehu_id']==2)  //1重庆客户  2其他客户
                {

                    // 获取当前登录用户
                    $user = $this->auth->getUser();

                    // 检查1：用户是否有有效的套餐卡 (全场通用)
                    $validSetmeal = db('setmeal_log')->where([
                        'user_id' => $user->id,
                        'status' => 2, // 已支付
                        'effective_end' => ['>=', time()],
                    ])->find();

                    if (!$validSetmeal) { // 如果没有有效套餐，才进行押金和余额的判断

                        // 检查2：用户余额是否足够抵扣押金
                        if ($user->money < $hospital['yajin']) {


                            // 检查3：用户是否已缴纳过有效的押金
                            if (!$user->deposit_id || $user->deposit <= 0) {

                                $return['msg']='请先缴纳押金';
                                $return['code']=201;
                                $return['data']['deposit_amount'] =  $hospital['yajin'];
                                $return['data']['hospital'] =  $hospital['id'];
                                // 三个条件都不满足，必须缴纳押金
                                return $return;
                            }
                        }
                    }
                }
                // --- 押金前置检查通过 ---








                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    //医院上设置了使用限制
                    $h = date('H');
                    $is = false;
                    if ($hospital['use_start'] > $hospital['use_end']) {
                        //开始时间大于结束时间
                        if ($h >= $hospital['use_start'] && $hospital['use_start'] <= 23) {
                            $is = true;
                        } else {
                            if ($h >= 0 && $h <= $hospital['use_end']) {
                                $is = true;
                            }
                        }
                    } else {
                        //开始时间小于结束时间
                        if ($h >= $hospital['use_start'] && $h <= $hospital['use_end']) {
                            $is = true;
                        }
                    }
                } else {
                    $is = true;
                }
                if ($is) {
                    if ($equipment['use_status'] == 1) {
                        if ($equipment['use_status'] == 1) {
                            $cab = new cab();
                            //查询设备所属医院信息
                            $hospital = db('hospital')->where(['id' => $equipment['hospitals_id']])->find();
                            $equipment['charging_rule'] = $hospital['charging_rule']; //计费规则:1=按小时计费,2=包干价计费
                            $equipment['price'] = $hospital['price']; //租赁价格(元)
                            $equipment['hourlong'] = $hospital['hourlong']; //计费周期(时)
                            $equipment['freedt'] = $hospital['freedt']; //免费时间（分）
                            $equipment['contract_price'] = $hospital['contract_price']; //包干价格
                            $equipment['hardware_type'] = $equipment['hardware_type']; //设备类型
                            $equipment['hospital_addr'] = $hospital['addr']; //设备地址
                            $equipment['equipment_name'] = $equipment['sn'] . '_' . $equipment['mainname']; //设备名称
                            $return['success'] = true;
                            $return['data'] = $equipment;
                            $return['msg'] = '可以租用';
                        } else {
                            if ($equipment['use_status'] == 2) {
                                $return['msg'] = '设备租用中';
                            } elseif ($equipment['use_status'] == 3) {
                                $return['msg'] = '设备故障';
                            }
                        }
                    } else {
                        $return['msg'] = '本地锁已被占用';
                    }
                } else {
                    $return['msg'] = '只能在' . $hospital['use_start'] . '点-' . $hospital['use_end'] . '点使用';
                }
            } else {
                $return['msg'] = '设备不存在';
            }
        } else {
            $return['msg'] = '参数错误';
        }
     
        return $return;
    }
    
    public function equipmentInfo() {
        $id = input('id', null);
        if ($id) {
            $equipment_info = db('equipment')->where(['id' => $id])->find();
            if ($equipment_info) {
                $hospital = db('hospital')->where(['id' => $equipment_info['hospitals_id']])->find();
                $hospital['use_start'] = (int) $hospital['use_start'];
                $hospital['use_end'] = (int) $hospital['use_end'];
                $is = true;
                if ($is) {
//                    $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
//                    if ($equipment['status'] == 1) {
//                        if ($equipment_info['status'] == 1) {
//                            
//                        } else {
//                            if ($equipment_info['status'] == 2) {
//                                $return['msg'] = '设备租用中';
//                            } elseif ($equipment_info['status'] == 3) {
//                                $return['msg'] = '设备故障';
//                            }
//                        }
//                    } else {
//                        $return['msg'] = '主设备被禁用';
//                    }
                    $cab = new cab();
                            //查询设备所属医院信息
                            $hospital = db('hospital')->where(['id' => $equipment_info['hospitals_id']])->find();
                            $equipment_info['charging_rule'] = $hospital['charging_rule']; //计费规则:1=按小时计费,2=包干价计费
                            $equipment_info['price'] = $hospital['price']; //租赁价格(元)
                            $equipment_info['hourlong'] = $hospital['hourlong']; //计费周期(时)
                            $equipment_info['freedt'] = $hospital['freedt']; //免费时间（分）
                            $equipment_info['contract_price'] = $hospital['contract_price']; //包干价格
                            $equipment_info['hardware_type'] = $equipment_info['hardware_type']; //设备类型
                            $equipment_info['hospital_addr'] = $hospital['addr']; //设备地址
                            $equipment_info['equipment_name'] = $equipment_info['sn'] . '_' . $equipment_info['mainname']; //设备名称
                            $return['success'] = true;
                            $return['data'] = $equipment_info;
                            $return['msg'] = '可以租用';
                }
            } else {
                $return['msg'] = '设备不存在';
            }
        } else {
            $return['msg'] = '参数错误';
        }
        $this->success('加载成功',$return);
    }

    /*
     * 上传电量
     */

    public function canzy() {
        $id = input('equipment_info_id', null);
        if ($id) {
            $equipment_info = db('equipment_info')->where(['id' => $id])->find();
            $data = array(
                'voltage' => hexdec(input('power')),
                'voltagetime' => time(),
            );
            //$res = db('equipment')->where(['id'=>$equipment_info['equipment_id']])->update($data);
            $res = db('equipment_info')->where(['id' => $equipment_info['id']])->update($data);
            $this->success('电压上报成功');
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 查询文章列表
     * types 文章类型
     */

    public function getArticleList() {
        $types = input('types', null);
        if ($types) {
            switch ($types) {
                case 'page':
                    $category_id = 15;
                    break;
                default:
                    $this->error('类型错误');
                    break;
            }
            $list = db('article')->where(['category_id' => $category_id])->field('id,title')->select();
            $this->success('数据查询成功', $list);
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 查询文章信息
     * id 文章id
     */

    public function getArticleInfo() {
        $id = $this->request->param('id', null);
        if ($id) {
            $info = db('article')->where(['id' => $id])->find();
            if ($info) {
                $this->success('数据查询成功', $info);
            } else {
                $this->error('数据不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 查询配置信息
     */

    public function getConfig() {
        $list = db('config')->where('1=1')->select();
        $lists = array();
        foreach ($list as $k => $v) {
            $lists[$list[$k]['name']] = $list[$k]['value'];
        }
        $this->success('数据查询成功', $lists);
    }

    //开锁介绍
    public function lockinfo(){
        $sn = $this->request->param('kpl');
        $equipment = Db::name('equipment')->where(['sn'=>$sn])->find();

        $hospital = Db::name('hospital')->where(['id'=>$equipment['hospitals_id']])->find();
        $user = Db::name('user')->where(['id'=>$this->auth->id])->find();
        $meal =Db::name('setmeal_log')->where(['user_id'=>$this->auth->id,'status'=>2,'effective_end'=>['>',time()]])->find();
        $data=[
            'sn'=>$equipment['sn'],
            'address'=>$equipment['address'],
            'hourlong'=>$hospital['hourlong'],
            'price'=>$hospital['price'],
            'money'=>$user['money'],
            'mealtime'=> $meal?date('Y-m-d',$meal['effective_end']):'',
        ];
        $this->success('ok',$data);
    }

}
