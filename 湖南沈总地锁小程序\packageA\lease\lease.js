// packageA/lease/lease.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		id: '', // 设备ID
		order_id: '', // 订单ID
		equipment: {}, // 设备信息
		timer:null,
		status_show:false,
		flags:true,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			id: options.id,
		})
		this.getequipment()
	},

	// 锁车
	handleSuo(){

	},

	// 获取设备信息
	getequipment() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Ajax/equipmentInfo', params).then(res => {
			const {	code,	data,msg} = res //接口数据
			if (code == 1) {
				that.setData({
					equipment: data.data,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
		})
	},

	// 3-租借开锁
	getunLock() {
		let that = this
		let params = {
			id: that.data.id
		}
		that.setData({
			flags:false,
		})
		app.post('Order/index', params).then(res => {
			const {	code,data,msg} = res //接口数据
			if (code == 1) {
				wx.showLoading({
					title: '正在锁车中...',
					mask: false,
				})
				that.setData({
					order_id: data,
					status_show:true,
				})
				let timer = setInterval(()=>{
					that.Order();
				},1000)
				this.setData({
					timer,
				}) 
			} else {
				// wx.showToast({
				// 	title: msg,
				// 	icon: 'none',
				// 	duration: 2000
				// })
			}
		}).catch((err) => {

		})
	},

	// 4-订单开始使用
	Order() {
		console.log('订单开始')
		let that = this
		let params = {
			order_id: that.data.order_id
		}
		app.post('Order/orderUse', params).then(res => {
			console.log('new新的');
			const {	code,data,	msg} = res //接口数据
			if (code == 1) {
				console.log('开锁了');
				wx.showToast({
					title: '锁车成功',
					icon: 'none',
					duration: 2000
				})
				clearInterval(this.data.timer)
				setTimeout(()=>{
					wx.navigateBack();
				},1000)
			} else {
				// wx.showToast({
				// 	title: msg,
				// 	icon: 'none',
				// 	duration: 2000
				// })
			}
		}).catch((err) => {

		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		clearInterval(this.data.timer)
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})