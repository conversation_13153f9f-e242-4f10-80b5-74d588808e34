# 订单抵扣功能实现说明

## 功能概述

实现了订单结束时的智能抵扣功能，按照优先级自动抵扣用户的套餐、押金、余额，剩余金额通过微信支付完成。

## 抵扣优先级

1. **套餐抵扣**：如果用户有生效的套餐，直接免费
2. **押金抵扣**：优先使用用户押金
3. **余额抵扣**：其次使用用户账户余额  
4. **微信支付**：最后剩余金额通过微信支付

## 核心功能特性

### 1. 智能抵扣逻辑
- 自动按优先级进行抵扣
- 支持多种抵扣方式组合
- 详细记录每步抵扣过程

### 2. 押金退款处理
- 当押金大于订单金额时，自动退还多余押金
- 创建退款记录，便于财务对账
- 支持微信退款接口调用

### 3. 完整的日志记录
- 每种抵扣方式都有独立的支付记录
- JSON格式记录完整支付过程
- 便于问题排查和财务对账

### 4. 事务安全保障
- 使用数据库事务确保数据一致性
- 异常情况自动回滚
- 防止数据不一致问题

## 数据库变更

### fa_order表新增字段
- `deposit_deducted`: 押金抵扣金额
- `balance_deducted`: 余额抵扣金额
- `remaining_amount`: 剩余需支付金额
- `payment_detail`: 支付明细JSON
- `setmeal_log_id`: 使用的套餐记录ID

### fa_pay表新增字段
- `deduction_type`: 抵扣类型
- `parent_pay_id`: 关联支付ID
- `refund_amount`: 退款金额

### fa_pay表types字段扩展
- 新增：5=套餐抵扣, 6=押金抵扣, 7=余额抵扣

## 使用场景示例

### 场景1：套餐用户
```
用户有生效套餐 → 直接免费 → 订单完成
```

### 场景2：押金充足
```
押金100元，订单30元 → 抵扣30元，退款70元 → 订单完成
```

### 场景3：多重抵扣
```
押金50元，余额30元，订单100元
→ 押金抵扣50元，余额抵扣30元，微信支付20元
```

### 场景4：完全抵扣
```
押金80元，余额50元，订单100元
→ 押金抵扣80元，余额抵扣20元 → 订单完成
```

## 支付记录说明

每种抵扣方式都会在fa_pay表中创建对应记录：

1. **套餐抵扣记录**：types=5, deduction_type=5
2. **押金抵扣记录**：types=6, deduction_type=1
3. **余额抵扣记录**：types=7, deduction_type=2
4. **微信支付记录**：types=2, deduction_type=3
5. **押金退款记录**：types=1, deduction_type=4

## 部署步骤

1. **执行数据库变更**
   ```sql
   -- 执行 database_updates.sql 中的SQL语句
   ```

2. **代码已更新**
   - `application/common/logic/Order.php` 的 `orderEnd1` 方法已优化
   - 新增了完整的抵扣处理逻辑
   - 集成了微信退款功能

3. **测试验证**
   - 使用 `测试抵扣功能.php` 检查数据库字段
   - 测试各种抵扣场景
   - 验证数据记录的完整性
   - 确认退款功能正常

4. **配置检查**
   - 确认 `application/extra/wxali.php` 配置正确
   - 验证微信证书文件路径有效
   - 测试微信退款接口连通性

## 微信退款功能

### 退款触发条件
当用户押金大于订单费用时，系统会自动处理多余押金的退款：

1. **自动退款**：押金抵扣后如有剩余，立即发起微信退款
2. **退款记录**：创建独立的退款支付记录，便于追踪
3. **状态管理**：退款状态实时更新，支持回调处理

### 退款流程
```
1. 计算剩余押金 = 用户押金 - 订单费用
2. 创建退款支付记录（types=1, deduction_type=4）
3. 调用微信退款接口
4. 更新退款记录状态为"退款中"
5. 等待微信退款回调确认
```

### 退款配置
系统使用 `config('wxali.wx')` 获取微信配置：
- `xcx.appid`: 小程序AppID
- `sh.mch_id`: 商户号
- `sh.key`: 商户密钥
- `cart.sslcert_path`: 证书路径
- `cart.sslkey_path`: 证书密钥路径

## 注意事项

1. **微信退款接口**：已完整实现微信退款功能，基于现有的wechatapppay类

2. **事务处理**：所有抵扣操作都在事务中执行，确保数据一致性

3. **日志记录**：详细的支付明细以JSON格式存储，便于后续查询和分析

4. **向后兼容**：新功能不影响现有订单的处理逻辑

5. **退款安全**：退款失败时会自动清理相关记录，避免数据不一致

## 扩展功能

后续可以基于此功能扩展：
- 抵扣优先级配置
- 抵扣限额设置
- 更复杂的套餐规则
- 抵扣统计报表
