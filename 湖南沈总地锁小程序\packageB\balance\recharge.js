// packageB/balance/recharge.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '', // 充值套餐ID
    quickAmounts: [], // 快捷金额
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getInfo()
  },
  getInfo(){
    let that = this
    let params = {}
		app.post('order/recharge_page_info', params).then(res => {
			const {	code,	data,	msg} = res //接口数据
			if (code == 1) {
        that.setData({
          quickAmounts: data,
        });
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			
		})
  },
  /**
   * 选择快捷金额
   */
  selectQuickAmount(e) {
    const item = e.currentTarget.dataset.item
    this.setData({
      id: item.id,
    })
  },

  /**
   * 确认充值
   */
  confirmRecharge() {
    if (!this.data.id) {
      wx.showToast({
        title: '请选择充值套餐',
        icon: 'none',
        duration: 2000
      })
      return
    }
    
    if (this.data.loading) {
      return
    }
    
    this.setData({ loading: true })
    
    // 第一步：创建充值订单
    this.createRechargeOrder()
  },

  /**
   * 创建充值订单
   */
  createRechargeOrder() {
    let that = this
    let params = {
      id: that.data.id,
    }
    app.post('Order/rechargeCreate', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        // 创建成功，发起支付
        that.startPayment(data.pay_id)
      } else {
        that.setData({ loading: false })
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      that.setData({ loading: false })
      wx.showToast({
        title: '创建充值订单失败',
        icon: 'none',
        duration: 2000
      })
      console.log('创建充值订单失败', err)
    })
  },

  /**
   * 发起支付
   */
  startPayment(payId) {
    let that = this
    let params = {
      pay_id: payId
    }
    
    app.post('Order/rechargePay', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        // 调用微信支付
        that.callWechatPay(data)
      } else {
        that.setData({ loading: false })
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      that.setData({ loading: false })
      wx.showToast({
        title: '发起支付失败',
        icon: 'none',
        duration: 2000
      })
      console.log('发起支付失败', err)
    })
  },

  /**
   * 调用微信支付
   */
  callWechatPay(payData) {
    let that = this
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success() {
        that.setData({ loading: false })
        wx.showToast({
          title: "充值成功",
          icon: "success",
          duration: 2000
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      },
      fail(res) {
        that.setData({ loading: false })
        console.log('支付失败', res)
        
        if (res.errMsg.includes('cancel')) {
          wx.showToast({
            title: "支付已取消",
            icon: "none",
            duration: 2000
          })
        } else {
          wx.showToast({
            title: "支付失败",
            icon: "none",
            duration: 2000
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
