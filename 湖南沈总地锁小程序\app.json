{"pages": ["pages/index/start", "pages/index/index", "pages/index/my", "pages/index/login"], "subpackages": [{"root": "packageA", "pages": ["stores/list", "package/index", "package/stores", "rental/index", "rental/detail", "stores/detail", "success/success", "lease/lease", "lockset/lockset"]}, {"root": "packageB", "pages": ["order/index", "order/details", "pay/index", "pay/succeed", "instruction/index", "setrecords/index", "charge/index", "balance/index", "balance/recharge"]}], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation", "<PERSON><PERSON><PERSON><PERSON>"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "地锁", "navigationBarTextStyle": "black"}, "tabBar": {"color": "#999", "selectedColor": "#2298F9", "backgroundColor": "#fff", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "image/foot_sy.png", "selectedIconPath": "image/foot_syb.png"}, {"pagePath": "pages/index/my", "text": "我的", "iconPath": "image/foot_wd.png", "selectedIconPath": "image/foot_wdb.png"}]}, "usingComponents": {"parser": "/utils/parser.min/parser"}, "sitemapLocation": "sitemap.json"}