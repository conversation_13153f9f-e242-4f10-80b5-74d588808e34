<?php

namespace app\common\logic;

use think\Db;
use weixin\Wechatapppay;

/**
 * 订单相关的通用业务逻辑类
 */
class Order
{
    /**
     * 处理结束订单的逻辑
     * @param array $order_info 订单信息
     */
    public function orderEnd($order_info)
    {
        trace('进入orderEnd方法');

        $return = array(
            'success' => true,
            'msg' => '',
        );
        $time = time();
        if ($order_info['status'] == 1) {
            $timelong = ceil(($time - $order_info['createtime']) / 3600); //计算时长（小时）
            $timelong_fenzhong = ceil(($time - $order_info['createtime']) / 60); //计算时长（分钟）
            $overtime = 0; //超时时长
            $overtime_money = 0; //超时费用
            $normal_money = 0; //时间段内归还费用

            $hospital = Db::name('hospital')->where(['id' => $order_info['hospital_id']])->find();
            if ($hospital['charging_rule'] == 1) {
                //按小时计费
                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    //开启了时间限制
                    if ($time <= $order_info['answer_return_time'] && $order_info['answer_return_time'] != '') {
                        //没有超时归还
                        $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                    } else {
                        //超时归还
                        $money = $normal_money = (ceil(($order_info['answer_return_time'] - $order_info['createtime']) / 3600) / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                        //计算超时费用
                        $money += $overtime_money = ceil((($time - $order_info['answer_/return_time']) / 3600)) * $hospital['overtime_price'];
                        //超时时长
                        $overtime = ceil((($time - $order_info['answer_return_time']) / 3600));
                    }
                } else {
                    //没有开启时间限制
                    $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                }
            } else {
                //包干计费
                $money = $normal_money = $hospital['contract_price'];
                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    if ($order_info['answer_return_time'] < $time && $order_info['answer_return_time'] != '') {
                        //超时归还
                        //计算超时费用
                        $money += $overtime_money = ceil((($time - $order_info['answer_return_time']) / 3600)) * $hospital['overtime_price'];
                        $overtime = ceil((($time - $order_info['answer_return_time']) / 3600));
                    }
                }
            }
            $order_update_data = array(
                'status' => 3,

                'returntime' => $time,
                'timelong' => $timelong,
                'timelong_fenzhong' => $timelong_fenzhong,
                'money' => $money,
                'really_money' => $money,
                'overtime' => $overtime,
                'overtime_money' => $overtime_money,
                'normal_money' => $normal_money,
                'charging_rule' => $hospital['charging_rule'],
            );
//            print_r($order_update_data);die();
            if ($timelong_fenzhong > $order_info['hospital_freedt']) {
                //如果使用时长大于免费时间
                $order_update_data['pay_status'] = 1;
            } else {
                $exemption_count = Db::name('platform')->where(['id' => $order_info['platform_id']])->value('exemption_count');
                $order_exemption_count = Db::name('order')->where([
                    'returntime' => array('between', array(
                        strtotime(date('Y-m-d 00:00:00')),
                        strtotime(date('Y-m-d 23:59:59'))
                    )),
                    'user_id' => $order_info['user_id'],
                    'pay_status' => 3
                ])->count();
                if ($order_exemption_count < $exemption_count) {
                    $order_update_data['pay_status'] = 3;
                } else {
                    $order_update_data['pay_status'] = 1;
                }
            }
            if ($order_update_data['pay_status'] == 1) {//订单正常支付 判断一下用户是否套餐卡
                $log_where = [
                    'user_id' => $order_info['user_id'],
                    'effective_start' => ['<=', time()],
                    'effective_end' => ['>=', time()],
                    'deletetime' => NULL,
                    'status' => 3,
                ];
                $setmeal_log = Db::name('setmeal_log')->where($log_where)->find();
                if ($setmeal_log) {
                    $order_update_data['pay_status'] = 5;
                }
            }
            $order_update_data['actreturntime'] = time();
            $order_update_data['status']=3;
            $res = Db::name('order')->where(['id' => $order_info['id']])->update($order_update_data);
            if (!$res) {
                $return['success'] = false;
                $return['msg'] = '更新状态失败';
            } else {

                $hardware_type = Db::name('equipment')->where(['id' => $order_info['equipment_id']])->value('hardware_type');
                if ($hardware_type == 1) {
                    $equipment_info_update_data = array(
                        'use_status' => 1,
                    );
                    //更新设备信息为 = 未租用
                    Db::name('equipment')->where(['id' => $order_info['equipment_id']])->update($equipment_info_update_data);
                }
                //重新查询更改后的订单数据
                $order_info = Db::name('order')->where(['id' => $order_info['id']])->find();
                $return['data'] = $order_info;
//                $user = db('user')->where(['id' => $order_info['user_id']])->find();
//                $Wechat = new Wechat();
//                $openid = $user['openid'];
//                $template_id = 'ky02g-3lrwJePjt8_sKwCPkhMFMQOZ9ND2-SA_TLmDo';
//                $url = 'pages/orders/orders?status=1';
//                $data = array(
//                    'time1' => array('value' => date('Y-m-d H:i:s',$order_info['createtime']),),
//                    'character_string2' => array('value' => $order_info['sn'],),
//                    'thing3' => array('value' => '共享商品租赁未结束订单',),
//                    'amount4' => array('value' => $order_info['money'] . '元',),
//                    'thing5' => array('value' => '您的本次服务已经完成，请及时支付您为完成的订单，支付完成后请到首页，提现押金！！！',),
//                );
//                $Wechat->newsSendout($openid, $template_id, $url, $data);
            }
        }
        //验证是否短时免单  $order_info['timelong_fenzhong'] > $order_info['hospital_freedt']
        if ($order_info['pay_status'] == 1 && $return['success'] == true) {



            //不短时免单
            //查询是否已经生成支付订单
            $pay = Db::name('pay')->where(['order_id' => $order_info['id']])->find();
            if (!$pay) {
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $order_info['user_id'],
                    'types' => 2,
                    'status' => 3,
                    'money' => $order_info['money'],
                    'order_id' => $order_info['id'],
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $pay_id = Db::name('pay')->insertGetId($pay_data);
                if (!$pay_id) {
                    $return['success'] = false;
                    $return['msg'] = '生成支付订单失败';
                }
            }
        }
        return $return;
    }



    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (Db::name($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }



    /**
     * 处理四轮地锁结束订单的逻辑 - 集成抵扣功能
     * @param array $order_info 订单信息
     */
    public function orderEnd1($order_info) {
        trace('=== 进入orderEnd1方法 ===');
        trace('orderEnd1 - 输入参数 $order_info:');
        trace($order_info);

        $return = array(
            'success' => true,
            'msg' => '',
        );
        trace('orderEnd1 - 初始化返回数据 $return:');
        trace($return);

        $time = time(); // 当前时间戳
        trace('orderEnd1 - 当前时间戳 $time: ' . $time);
        trace('orderEnd1 - 当前时间格式化: ' . date('Y-m-d H:i:s', $time));

        trace('orderEnd1 - 检查订单状态 $order_info[status]: ' . $order_info['status']);
        if ($order_info['status'] == 1) {
            trace('orderEnd1 - 订单状态为1，开始处理结束订单逻辑');

            // 计算使用时长
            $createtime = $order_info['createtime'];
            trace('orderEnd1 - 订单创建时间 $createtime: ' . $createtime);
            trace('orderEnd1 - 订单创建时间格式化: ' . date('Y-m-d H:i:s', $createtime));

            $time_diff_seconds = $time - $createtime;
            trace('orderEnd1 - 时间差（秒）$time_diff_seconds: ' . $time_diff_seconds);

            $timelong = ceil($time_diff_seconds / 3600); //计算时长（小时）
            $timelong_fenzhong = ceil($time_diff_seconds / 60); //计算时长（分钟）
            trace('orderEnd1 - 使用时长（小时）$timelong: ' . $timelong);
            trace('orderEnd1 - 使用时长（分钟）$timelong_fenzhong: ' . $timelong_fenzhong);

            $overtime = 0; //超时时长
            $overtime_money = 0; //超时费用
            $normal_money = 0; //时间段内归还费用
            trace('orderEnd1 - 初始化费用变量 - $overtime: ' . $overtime . ', $overtime_money: ' . $overtime_money . ', $normal_money: ' . $normal_money);

            trace('orderEnd1 - 查询医院信息，hospital_id: ' . $order_info['hospital_id']);
            $hospital = Db::name('hospital')->where(['id' => $order_info['hospital_id']])->find();
            trace('orderEnd1 - 医院信息 $hospital:');
            trace($hospital);
            trace('orderEnd1 - 计费规则 $hospital[charging_rule]: ' . $hospital['charging_rule']);
            if ($hospital['charging_rule'] == 1) {
                trace('orderEnd1 - 按小时计费模式');
                trace('orderEnd1 - 时间限制设置 use_start: ' . $hospital['use_start'] . ', use_end: ' . $hospital['use_end']);

                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    trace('orderEnd1 - 开启了时间限制');
                    trace('orderEnd1 - 预约归还时间 answer_return_time: ' . $order_info['answer_return_time']);
                    trace('orderEnd1 - 预约归还时间格式化: ' . ($order_info['answer_return_time'] ? date('Y-m-d H:i:s', $order_info['answer_return_time']) : '未设置'));

                    if ($time <= $order_info['answer_return_time'] && $order_info['answer_return_time'] != '') {
                        trace('orderEnd1 - 没有超时归还');
                        trace('orderEnd1 - 计费参数 - timelong: ' . $timelong . ', hospital_hourlong: ' . $order_info['hospital_hourlong'] . ', hospital_price: ' . $order_info['hospital_price']);
                        $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                        trace('orderEnd1 - 计算费用（无超时）$money: ' . $money);
                    } else {
                        trace('orderEnd1 - 超时归还');
                        // 计算正常时间段费用
                        $normal_time_hours = ceil(($order_info['answer_return_time'] - $order_info['createtime']) / 3600);
                        trace('orderEnd1 - 正常时间段小时数 $normal_time_hours: ' . $normal_time_hours);
                        $money = $normal_money = ($normal_time_hours / $order_info['hospital_hourlong']) * $order_info['hospital_price'];
                        trace('orderEnd1 - 正常时间段费用 $normal_money: ' . $normal_money);

                        // 计算超时费用
                        $overtime = ceil(($time - $order_info['answer_return_time']) / 3600);
                        $overtime_money = $overtime * $hospital['overtime_price'];
                        trace('orderEnd1 - 超时时长（小时）$overtime: ' . $overtime);
                        trace('orderEnd1 - 超时单价 overtime_price: ' . $hospital['overtime_price']);
                        trace('orderEnd1 - 超时费用 $overtime_money: ' . $overtime_money);

                        $money += $overtime_money;
                        trace('orderEnd1 - 总费用（正常+超时）$money: ' . $money);
                    }
                } else {
                    trace('orderEnd1 - 没有开启时间限制');
                    trace('orderEnd1 - 计费参数 - timelong: ' . $timelong . ', hospital_hourlong: ' . $order_info['hospital_hourlong'] . ', hospital_price: ' . $order_info['hospital_price']);
                    $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                    trace('orderEnd1 - 计算费用（无时间限制）$money: ' . $money);
                }
            } else {
                trace('orderEnd1 - 包干计费模式');
                $money = $normal_money = $hospital['contract_price'];
                trace('orderEnd1 - 包干价格 contract_price: ' . $hospital['contract_price']);
                trace('orderEnd1 - 包干费用 $money: ' . $money . ', $normal_money: ' . $normal_money);

                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    trace('orderEnd1 - 包干模式下检查时间限制');
                    trace('orderEnd1 - 预约归还时间: ' . $order_info['answer_return_time'] . ', 当前时间: ' . $time);

                    if ($order_info['answer_return_time'] < $time && $order_info['answer_return_time'] != '') {
                        trace('orderEnd1 - 包干模式超时归还');
                        $overtime = ceil(($time - $order_info['answer_return_time']) / 3600);
                        $overtime_money = $overtime * $hospital['overtime_price'];
                        trace('orderEnd1 - 包干模式超时时长 $overtime: ' . $overtime);
                        trace('orderEnd1 - 包干模式超时费用 $overtime_money: ' . $overtime_money);

                        $money += $overtime_money;
                        trace('orderEnd1 - 包干模式总费用 $money: ' . $money);
                    }
                }
            }

            trace('orderEnd1 - 费用计算完成，开始初始化订单更新数据');
            // 初始化订单更新数据
            $order_update_data = array(
                'status' => 2, // 订单状态：已归还/未支付
                'returntime' => $time, // 归还时间
                'timelong' => $timelong, // 使用时长（小时）
                'timelong_fenzhong' => $timelong_fenzhong, // 使用时长（分钟）
                'money' => $money, // 应付费用
                'really_money' => $money, // 系统计算的实际费用
                'overtime' => $overtime, // 超时时长
                'overtime_money' => $overtime_money, // 超时费用
                'normal_money' => $normal_money, // 正常时间段费用
                'charging_rule' => $hospital['charging_rule'], // 计费规则
                'deposit_deducted' => 0.00, // 押金抵扣金额
                'balance_deducted' => 0.00, // 余额抵扣金额
                'remaining_amount' => $money, // 剩余需支付金额
                'setmeal_log_id' => 0, // 套餐记录ID
                'payment_detail' => '', // 支付明细JSON
            );
            trace('orderEnd1 - 初始化订单更新数据 $order_update_data:');
            trace($order_update_data);

            // 判断是否需要支付
            trace('orderEnd1 - 开始判断是否需要支付');
            trace('orderEnd1 - 使用时长（分钟）: ' . $timelong_fenzhong . ', 免费时间（分钟）: ' . $order_info['hospital_freedt']);

            if ($timelong_fenzhong > $order_info['hospital_freedt']) {
                trace('orderEnd1 - 使用时长大于免费时间，需要支付');
                $order_update_data['pay_status'] = 1; // 需要支付
                trace('orderEnd1 - 设置支付状态为1（需要支付）');
            } else {
                trace('orderEnd1 - 使用时长在免费时间内，检查短时免单次数');

                // 检查短时免单次数
                trace('orderEnd1 - 查询平台免单次数配置，platform_id: ' . $order_info['platform_id']);
                $exemption_count = Db::name('platform')->where(['id' => $order_info['platform_id']])->value('exemption_count');
                trace('orderEnd1 - 平台每日免单次数 $exemption_count: ' . $exemption_count);

                $today_start = strtotime(date('Y-m-d 00:00:00'));
                $today_end = strtotime(date('Y-m-d 23:59:59'));
                trace('orderEnd1 - 今日时间范围: ' . date('Y-m-d H:i:s', $today_start) . ' 到 ' . date('Y-m-d H:i:s', $today_end));

                $order_exemption_count = Db::name('order')->where([
                    'returntime' => array('between', array($today_start, $today_end)),
                    'user_id' => $order_info['user_id'],
                    'pay_status' => 3
                ])->count();
                trace('orderEnd1 - 用户今日已免单次数 $order_exemption_count: ' . $order_exemption_count);
                trace('orderEnd1 - 查询条件 - user_id: ' . $order_info['user_id'] . ', pay_status: 3（短时免单）');

                if ($order_exemption_count < $exemption_count) {
                    trace('orderEnd1 - 未达到免单次数上限，设置为短时免单');
                    $order_update_data['pay_status'] = 3; // 短时免单
                } else {
                    trace('orderEnd1 - 已达到免单次数上限，需要支付');
                    $order_update_data['pay_status'] = 1; // 需要支付
                }
                trace('orderEnd1 - 最终支付状态 pay_status: ' . $order_update_data['pay_status']);
            }

            // 如果需要支付，进行抵扣逻辑处理
            trace('orderEnd1 - 检查是否需要进行抵扣逻辑，pay_status: ' . $order_update_data['pay_status']);
            if($order_update_data['pay_status'] == 1) {
                trace('orderEnd1 - 需要支付，开始抵扣逻辑处理');

                // 第一步：检查用户是否有生效的套餐
                trace('orderEnd1 - 第一步：检查用户套餐');
                $current_time = time();
                $log_where = [
                    'user_id' => $order_info['user_id'],
                    'effective_start' => ['<=', $current_time],
                    'effective_end' => ['>=', $current_time],
                    'deletetime' => NULL,
                    'status' => 2,
                ];
                trace('orderEnd1 - 套餐查询条件 $log_where:');
                trace($log_where);
                trace('orderEnd1 - 当前时间: ' . date('Y-m-d H:i:s', $current_time));

                $setmeal_log = Db::name('setmeal_log')->where($log_where)->find();
                trace('orderEnd1 - 套餐查询结果 $setmeal_log:');
                trace($setmeal_log);

                if($setmeal_log) {
                    trace('orderEnd1 - 找到有效套餐，进行套餐抵扣');
                    // 套餐抵扣 - 完全免费
                    $order_update_data['pay_status'] = 5; // 套餐抵扣
                    $order_update_data['setmeal_log_id'] = $setmeal_log['id'];
                    $order_update_data['remaining_amount'] = 0.00;
                    $order_update_data['payment_detail'] = json_encode([
                        'type' => 'setmeal',
                        'setmeal_log_id' => $setmeal_log['id'],
                        'amount' => $money,
                        'message' => '套餐抵扣'
                    ], JSON_UNESCAPED_UNICODE);
                    trace('orderEnd1 - 套餐抵扣完成，更新数据:');
                    trace([
                        'pay_status' => $order_update_data['pay_status'],
                        'setmeal_log_id' => $order_update_data['setmeal_log_id'],
                        'remaining_amount' => $order_update_data['remaining_amount'],
                        'payment_detail' => $order_update_data['payment_detail']
                    ]);
                } else {
                    trace('orderEnd1 - 没有找到有效套餐，进行押金和余额抵扣');
                    // 没有套餐，进行押金和余额抵扣
                    trace('orderEnd1 - 调用processPaymentDeduction方法');
                    trace('orderEnd1 - 抵扣参数 - user_id: ' . $order_info['user_id'] . ', money: ' . $money . ', order_id: ' . $order_info['id']);

                    $deduction_result = $this->processPaymentDeduction($order_info['user_id'], $money, $order_info['id']);
                    trace('orderEnd1 - 抵扣结果 $deduction_result:');
                    trace($deduction_result);

                    if($deduction_result['success']) {
                        trace('orderEnd1 - 抵扣成功，更新订单抵扣信息');
                        // 更新订单抵扣信息
                        $order_update_data['deposit_deducted'] = $deduction_result['deposit_deducted'];
                        $order_update_data['balance_deducted'] = $deduction_result['balance_deducted'];
                        $order_update_data['remaining_amount'] = $deduction_result['remaining_amount'];
                        $order_update_data['payment_detail'] = $deduction_result['payment_detail'];
                        trace('orderEnd1 - 抵扣信息更新:');
                        trace([
                            'deposit_deducted' => $order_update_data['deposit_deducted'],
                            'balance_deducted' => $order_update_data['balance_deducted'],
                            'remaining_amount' => $order_update_data['remaining_amount'],
                            'payment_detail' => $order_update_data['payment_detail']
                        ]);

                        // 根据剩余金额设置支付状态
                        trace('orderEnd1 - 检查剩余金额，remaining_amount: ' . $deduction_result['remaining_amount']);
                        if($deduction_result['remaining_amount'] <= 0) {
                            trace('orderEnd1 - 已完全抵扣，设置订单状态为完成');
                            $order_update_data['pay_status'] = 1; // 已完全抵扣，但保持为1以便后续处理
                            $order_update_data['status'] = 3; // 订单完成
                            trace('orderEnd1 - 完全抵扣后状态:');
                            trace([
                                'pay_status' => $order_update_data['pay_status'],
                                'status' => $order_update_data['status']
                            ]);
                        }
                    } else {
                        trace('orderEnd1 - 抵扣失败: ' . $deduction_result['msg']);
                        $return['success'] = false;
                        $return['msg'] = $deduction_result['msg'];
                        trace('orderEnd1 - 返回错误结果 $return:');
                        trace($return);
                        return $return;
                    }
                }
            } else {
                trace('orderEnd1 - 无需支付或已设置为免单，跳过抵扣逻辑');
            }

            $order_update_data['actreturntime'] = time(); // 记录orderEnd方法执行时间
            trace('orderEnd1 - 设置实际归还时间 actreturntime: ' . $order_update_data['actreturntime']);
            trace('orderEnd1 - 最终订单更新数据 $order_update_data:');
            trace($order_update_data);

            // 开启事务
            trace('orderEnd1 - 开启数据库事务');
            Db::startTrans();

            trace('orderEnd1 - 开始更新订单信息，order_id: ' . $order_info['id']);
            // 更新订单信息
            $res = Db::name('order')->where(['id' => $order_info['id']])->update($order_update_data);
            trace('orderEnd1 - 订单更新结果 $res: ' . $res);
            if (!$res) {
                trace('orderEnd1 - 更新订单状态失败，回滚事务');
                Db::rollback();
                $return['success'] = false;
                $return['msg'] = '更新订单状态失败';
                trace('orderEnd1 - 返回失败结果:');
                trace($return);
                return $return;
            }

            // 更新设备状态
            trace('orderEnd1 - 查询设备硬件类型，equipment_id: ' . $order_info['equipment_id']);
            $hardware_type = Db::name('equipment')->where(['id' => $order_info['equipment_id']])->value('hardware_type');
            trace('orderEnd1 - 设备硬件类型 $hardware_type: ' . $hardware_type);

            if ($hardware_type == 1) {
                trace('orderEnd1 - 硬件类型为1，更新设备状态为未租用');
                $equipment_info_update_data = array(
                    'use_status' => 1, // 设备状态：未租用
                );
                trace('orderEnd1 - 设备更新数据 $equipment_info_update_data:');
                trace($equipment_info_update_data);
                $equipment_update_res = Db::name('equipment')->where(['id' => $order_info['equipment_id']])->update($equipment_info_update_data);
                trace('orderEnd1 - 设备状态更新结果: ' . $equipment_update_res);
            } else {
                trace('orderEnd1 - 硬件类型不为1，跳过设备状态更新');
            }

            // 提交事务
            trace('orderEnd1 - 提交数据库事务');
            Db::commit();

            //重新查询更改后的订单数据
            trace('orderEnd1 - 重新查询更新后的订单数据');
            $order_info = Db::name('order')->where(['id' => $order_info['id']])->find();
            trace('orderEnd1 - 更新后的订单信息 $order_info:');
            trace($order_info);
            $return['data'] = $order_info;
            trace('orderEnd1 - 设置返回数据 $return[data]');
        } else {
            trace('orderEnd1 - 订单状态不为1，跳过处理逻辑');
            trace('orderEnd1 - 当前订单状态: ' . $order_info['status']);
        }

        // 如果仍有剩余金额需要支付，生成支付订单
        trace('orderEnd1 - 检查是否需要生成支付订单');
        trace('orderEnd1 - 检查条件 - pay_status: ' . $order_info['pay_status'] . ', remaining_amount: ' . $order_info['remaining_amount'] . ', return_success: ' . ($return['success'] ? 'true' : 'false'));

        if ($order_info['pay_status'] == 1 && $order_info['remaining_amount'] > 0 && $return['success'] == true) {
            trace('orderEnd1 - 需要生成支付订单');

            //查询是否已经生成支付订单
            trace('orderEnd1 - 查询是否已存在支付订单，order_id: ' . $order_info['id']);
            $pay = Db::name('pay')->where(['order_id' => $order_info['id'], 'types' => 2])->find();
            trace('orderEnd1 - 支付订单查询结果 $pay:');
            trace($pay);

            if (!$pay) {
                trace('orderEnd1 - 不存在支付订单，开始生成新的支付订单');
                //生成微信支付订单
                $pay_sn = $this->getOrdersn('pay');
                trace('orderEnd1 - 生成支付订单号 $pay_sn: ' . $pay_sn);

                $pay_data = array(
                    'sn' => $pay_sn,
                    'user_id' => $order_info['user_id'],
                    'types' => 2, // 租用费用
                    'status' => 1, // 未支付
                    'money' => $order_info['remaining_amount'], // 剩余需支付金额
                    'order_id' => $order_info['id'],
                    'deduction_type' => 3, // 微信支付
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                trace('orderEnd1 - 支付订单数据 $pay_data:');
                trace($pay_data);

                $pay_id = Db::name('pay')->insertGetId($pay_data);
                trace('orderEnd1 - 支付订单创建结果 $pay_id: ' . $pay_id);

                if (!$pay_id) {
                    trace('orderEnd1 - 生成支付订单失败');
                    $return['success'] = false;
                    $return['msg'] = '生成支付订单失败';
                } else {
                    trace('orderEnd1 - 支付订单生成成功，pay_id: ' . $pay_id);
                }
            } else {
                trace('orderEnd1 - 支付订单已存在，跳过生成');
            }
        } else {
            trace('orderEnd1 - 不满足生成支付订单条件，跳过');
        }

        trace('orderEnd1 - 方法执行完成，最终返回结果 $return:');
        trace($return);
        trace('=== orderEnd1方法结束 ===');
        return $return;
    }

    /**
     * 处理支付抵扣逻辑（押金 -> 余额 -> 剩余支付）
     * @param int $user_id 用户ID
     * @param float $total_amount 订单总金额
     * @param int $order_id 订单ID
     * @return array 抵扣结果
     */
    private function processPaymentDeduction($user_id, $total_amount, $order_id) {
        trace('进入processPaymentDeduction');
        $result = [
            'success' => true,
            'msg' => '',
            'deposit_deducted' => 0.00, // 押金抵扣金额
            'balance_deducted' => 0.00, // 余额抵扣金额
            'remaining_amount' => $total_amount, // 剩余需支付金额
            'payment_detail' => '', // 支付明细JSON
            'refund_amount' => 0.00, // 押金退款金额
        ];

        try {
            // 获取用户信息
            $user = Db::name('user')->where(['id' => $user_id])->find();
            if (!$user) {
                $result['success'] = false;
                $result['msg'] = '用户不存在';
                return $result;
            }

            $current_deposit = floatval($user['deposit']); // 当前押金余额
            $current_balance = floatval($user['money']); // 当前账户余额
            $remaining_amount = floatval($total_amount); // 剩余需要支付的金额
            $payment_steps = []; // 支付步骤记录

            // 第一步：押金抵扣
            if ($current_deposit > 0 && $remaining_amount > 0) {
                if ($current_deposit >= $remaining_amount) {
                    // 押金充足，完全抵扣
                    $deposit_used = $remaining_amount; // 使用的押金金额
                    $deposit_refund = $current_deposit - $remaining_amount; // 需要退款的押金
                    $remaining_amount = 0; // 剩余金额为0

                    // 记录押金抵扣
                    $result['deposit_deducted'] = $deposit_used;
                    $result['refund_amount'] = $deposit_refund;

                    $payment_steps[] = [
                        'step' => 1,
                        'type' => 'deposit_deduction',
                        'amount' => $deposit_used,
                        'remaining_order' => $remaining_amount
                    ];

                    // 如果有剩余押金需要退款
                    if ($deposit_refund > 0) {
                        $payment_steps[] = [
                            'step' => 2,
                            'type' => 'deposit_refund',
                            'amount' => $deposit_refund,
                            'status' => 'pending'
                        ];

                        // 处理押金退款
                        $refund_result = $this->processDepositRefund($user, $deposit_refund, $order_id);
                        if (!$refund_result['success']) {
                            $result['success'] = false;
                            $result['msg'] = '押金退款处理失败：' . $refund_result['msg'];
                            return $result;
                        }
                    }

                    // 更新用户押金为0（全部使用）
                    Db::name('user')->where(['id' => $user_id])->update([
                        'deposit' => 0,
                        'deposit_id' => 0
                    ]);

                    // 记录押金抵扣支付记录
                    $this->createDeductionPayRecord($user_id, $order_id, $deposit_used, 6, 1, $user['deposit_id']);

                } else {
                    // 押金不足，部分抵扣
                    $deposit_used = $current_deposit; // 全部押金都用于抵扣
                    $remaining_amount -= $current_deposit; // 减去押金后的剩余金额

                    $result['deposit_deducted'] = $deposit_used;

                    $payment_steps[] = [
                        'step' => 1,
                        'type' => 'deposit_deduction',
                        'amount' => $deposit_used,
                        'remaining_order' => $remaining_amount
                    ];

                    // 更新用户押金为0
                    Db::name('user')->where(['id' => $user_id])->update([
                        'deposit' => 0,
                        'deposit_id' => 0
                    ]);

                    // 记录押金抵扣支付记录
                    $this->createDeductionPayRecord($user_id, $order_id, $deposit_used, 6, 1, $user['deposit_id']);
                }
            }

            // 第二步：余额抵扣
            if ($current_balance > 0 && $remaining_amount > 0) {
                if ($current_balance >= $remaining_amount) {
                    // 余额充足，完全抵扣
                    $balance_used = $remaining_amount;
                    $remaining_amount = 0;

                    $result['balance_deducted'] = $balance_used;

                    $payment_steps[] = [
                        'step' => count($payment_steps) + 1,
                        'type' => 'balance_deduction',
                        'amount' => $balance_used,
                        'remaining_order' => $remaining_amount
                    ];

                    // 扣减用户余额并记录日志
                    \app\common\model\User::money(-$balance_used, $user_id, '订单支付余额抵扣', 5, $order_id);

                    // 记录余额抵扣支付记录
                    $this->createDeductionPayRecord($user_id, $order_id, $balance_used, 7, 2);

                } else {
                    // 余额不足，部分抵扣
                    $balance_used = $current_balance;
                    $remaining_amount -= $current_balance;

                    $result['balance_deducted'] = $balance_used;

                    $payment_steps[] = [
                        'step' => count($payment_steps) + 1,
                        'type' => 'balance_deduction',
                        'amount' => $balance_used,
                        'remaining_order' => $remaining_amount
                    ];

                    // 扣减用户余额并记录日志
                    \app\common\model\User::money(-$balance_used, $user_id, '订单支付余额抵扣', 5, $order_id);

                    // 记录余额抵扣支付记录
                    $this->createDeductionPayRecord($user_id, $order_id, $balance_used, 7, 2);
                }
            }

            // 第三步：如果还有剩余金额，需要微信支付
            if ($remaining_amount > 0) {
                $payment_steps[] = [
                    'step' => count($payment_steps) + 1,
                    'type' => 'wechat_payment_required',
                    'amount' => $remaining_amount
                ];
            }

            // 设置最终结果
            $result['remaining_amount'] = $remaining_amount;
            $result['payment_detail'] = json_encode([
                'order_total' => $total_amount,
                'payment_process' => $payment_steps,
                'final_status' => $remaining_amount > 0 ? 'pending_payment' : 'completed'
            ], JSON_UNESCAPED_UNICODE);

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['msg'] = '抵扣处理异常：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 处理押金退款
     * @param array $user 用户信息
     * @param float $refund_amount 退款金额
     * @param int $order_id 订单ID
     * @return array 退款结果
     */
    private function processDepositRefund($user, $refund_amount, $order_id) {
        $result = [
            'success' => true,
            'msg' => ''
        ];

        try {
            // 获取原押金支付记录
            $original_pay = Db::name('pay')->where(['id' => $user['deposit_id']])->find();
            if (!$original_pay) {
                $result['success'] = false;
                $result['msg'] = '原押金支付记录不存在';
                return $result;
            }

            // 创建退款记录，使用原押金支付记录的信息
            $refund_pay_data = [
                'sn' => $this->getOrdersn('pay'),
                'user_id' => $user['id'],
                'types' => 1, // 押金充值类型
                'status' => 5, // 退款中
                'money' => $refund_amount,
                'order_id' => $order_id,
                'deduction_type' => 4, // 押金退款
                'parent_pay_id' => $user['deposit_id'], // 关联原押金支付记录
                'refund_amount' => $refund_amount,
                'transaction_id' => $original_pay['transaction_id'], // 复制原支付记录的微信交易号
                'createtime' => time(),
                'updatetime' => time(),
            ];

            $refund_pay_id = Db::name('pay')->insertGetId($refund_pay_data);
            if (!$refund_pay_id) {
                $result['success'] = false;
                $result['msg'] = '创建退款记录失败';
                return $result;
            }

            // 调用微信退款接口
            $refund_result = $this->wxRefund($refund_pay_data);
            if (!$refund_result['success']) {
                // 退款失败，删除刚创建的退款记录
                Db::name('pay')->where(['id' => $refund_pay_id])->delete();
                $result['success'] = false;
                $result['msg'] = '微信退款失败：' . $refund_result['msg'];
                return $result;
            }

            $result['msg'] = '押金退款处理成功，退款金额：' . $refund_amount . '元';

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['msg'] = '押金退款处理异常：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 微信退款方法（基于现有User.php中的wxRefund方法）
     * @param array $pay 支付记录
     * @return array 退款结果
     */
    private function wxRefund($pay) {
        $return = array(
            'success' => false,
            'msg' => ''
        );

        try {
            // 获取微信配置（使用config方式获取，与现有代码保持一致）
            $payconfig = config('wxali.wx');
            if (!$payconfig) {
                $return['msg'] = '微信配置获取失败';
                return $return;
            }

            $wxappid = $payconfig['xcx']['appid']; // 小程序appid
            $mch_id = $payconfig['sh']['mch_id']; // 商户号
            $wxkey = $payconfig['sh']['key']; // 商户key
            $apiclient_cert = $payconfig['cart']['sslcert_path']; // 证书路径
            $apiclient_key = $payconfig['cart']['sslkey_path']; // 证书路径

            // 退款回调地址
            $notify_url = 'https://' . $_SERVER['HTTP_HOST'] . '/api/pay/refundnotify';

            // 实例化微信支付类
            $wechatAppPay = new Wechatapppay($wxappid, $mch_id, $notify_url, $wxkey, $apiclient_cert, $apiclient_key);

            // 查询订单状态
            $sn = $pay['sn']; // 退款订单号
            $transaction_id = $pay['transaction_id']; // 微信交易号

            $wx_orderQuery = $wechatAppPay->orderQuery($sn);
            if ($wx_orderQuery['return_code'] == 'SUCCESS') {
                if ($wx_orderQuery['trade_state'] == 'SUCCESS') {
                    $money = $wx_orderQuery['total_fee']; // 原订单金额（分）

                    // 退款参数
                    $params = array();
                    $params['out_trade_no'] = $sn; // 商户订单号
                    $params['total_fee'] = $money; // 原订单金额（分）
                    $params['return_oid'] = $transaction_id; // 微信交易号
                    $params['notify_url'] = $notify_url; // 退款回调地址

                    // 发起退款
                    $wx_result = $wechatAppPay->refund($params);

                    if ($wx_result['return_code'] == 'SUCCESS') {
                        // 更新退款记录状态为退款中
                        Db::name('pay')->where(['id' => $pay['id']])->update(array(
                            'status' => 5, // 退款中
                            'updatetime' => time()
                        ));

                        $return['success'] = true;
                        $return['msg'] = '退款申请成功，等待处理';
                    } else {
                        $return['msg'] = '退款申请失败：' . ($wx_result['return_msg'] ?? '未知错误');
                    }
                } else {
                    // 处理不同的订单状态
                    switch ($wx_orderQuery['trade_state']) {
                        case 'REFUND':
                            $return['msg'] = '订单已退款';
                            break;
                        case 'NOTPAY':
                            $return['msg'] = '订单未支付';
                            break;
                        case 'CLOSED':
                            $return['msg'] = '订单已关闭';
                            break;
                        case 'REVOKED':
                            $return['msg'] = '订单已撤销';
                            break;
                        case 'USERPAYING':
                            $return['msg'] = '用户支付中';
                            break;
                        case 'PAYERROR':
                            $return['msg'] = '支付失败';
                            break;
                        default:
                            $return['msg'] = '订单状态错误：' . $wx_orderQuery['trade_state'];
                            break;
                    }
                }
            } else {
                $return['msg'] = '查询订单失败：' . ($wx_orderQuery['return_msg'] ?? '未知错误');
            }

        } catch (\Exception $e) {
            $return['msg'] = '退款处理异常：' . $e->getMessage();
        }

        return $return;
    }

    /**
     * 创建抵扣支付记录
     * @param int $user_id 用户ID
     * @param int $order_id 订单ID
     * @param float $amount 抵扣金额
     * @param int $types 支付类型
     * @param int $deduction_type 抵扣类型
     * @param int $parent_pay_id 关联支付ID（可选）
     * @return int 支付记录ID
     */
    private function createDeductionPayRecord($user_id, $order_id, $amount, $types, $deduction_type, $parent_pay_id = 0) {
        $pay_data = [
            'sn' => $this->getOrdersn('pay'),
            'user_id' => $user_id,
            'types' => $types, // 6=押金抵扣, 7=余额抵扣
            'status' => 2, // 已支付（抵扣完成）
            'money' => $amount,
            'order_id' => $order_id,
            'deduction_type' => $deduction_type, // 1=押金抵扣, 2=余额抵扣
            'parent_pay_id' => $parent_pay_id,
            'pay_time' => time(),
            'createtime' => time(),
            'updatetime' => time(),
        ];

        return Db::name('pay')->insertGetId($pay_data);
    }
}