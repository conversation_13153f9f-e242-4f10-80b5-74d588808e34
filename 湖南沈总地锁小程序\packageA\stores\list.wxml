<!--packageA/index/stores/list.wxml-->
<view class="container">
	<view class="search">
		<view>
			<image src="/image/icon_search.png" bind:tap="getSearch"></image>
			<input type="text" confirm-type='search' bindinput="handleInput" bindconfirm='getSearch' placeholder="搜索周边空闲共享地锁" placeholder-style="color:#999"/>
		</view>
	</view>
	<view class="list">
		<view class="nr" bind:tap="goUrl" data-index="{{index}}" wx:for="{{list}}" wx:key="index" wx:if="{{list.length > 0}}">
			<view class="xx level">
				<view class="pic">
					<image src="{{item.logo_image}}" mode="widthFix"></image>
				</view>
				<view class="text">
					<view class="name">{{item.name}}</view>
					<view class="numb">
						<view class="retable">可租借: {{item.available}}</view>
						<view class="total">车位共: {{item.intotal}}</view>
					</view>
					<view class="address" catchtap="goLocation" data-lat="{{item.latitude}}" data-lon="{{item.longitude}}" data-name="{{item.name}}" data-addr="{{item.addr}}">{{item.juli}}km|{{item.addr}}</view>
				</view>
			</view>
		</view>

		<view class='loading' wx:if="{{list.length > 0}}">{{loadTitle}}</view>

		<view class="zwsj" wx:if="{{list.length == 0}}">
			<image src="/image/icon_wsj.png" mode="widthFix"></image>
			<view>附近暂无门店</view>
		</view>
	</view>
</view>