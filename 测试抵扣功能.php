<?php
/**
 * 订单抵扣功能测试脚本
 * 使用方法：在浏览器中访问此文件，或通过命令行执行
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/start.php';

use app\common\logic\Order;

/**
 * 测试订单抵扣功能
 */
function testOrderDeduction() {
    echo "=== 订单抵扣功能测试 ===\n";
    
    // 创建Order逻辑类实例
    $orderLogic = new Order();
    
    // 测试场景1：模拟一个需要抵扣的订单
    $test_order = [
        'id' => 999, // 测试订单ID
        'user_id' => 1, // 测试用户ID
        'status' => 1, // 使用中
        'hospital_id' => 1,
        'equipment_id' => 1,
        'createtime' => time() - 3600, // 1小时前创建
        'hospital_freedt' => 10, // 免费时间10分钟
        'hospital_hourlong' => 1, // 按小时计费
        'hospital_price' => 50.00, // 每小时50元
        'platform_id' => 1
    ];
    
    echo "测试订单信息：\n";
    echo "- 订单ID: {$test_order['id']}\n";
    echo "- 用户ID: {$test_order['user_id']}\n";
    echo "- 使用时长: 1小时\n";
    echo "- 预计费用: 50元\n\n";
    
    // 调用orderEnd1方法
    try {
        $result = $orderLogic->orderEnd1($test_order);
        
        if ($result['success']) {
            echo "✅ 订单处理成功！\n";
            echo "返回数据：\n";
            print_r($result);
        } else {
            echo "❌ 订单处理失败：{$result['msg']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 测试异常：" . $e->getMessage() . "\n";
    }
}

/**
 * 测试数据库字段是否正确添加
 */
function testDatabaseFields() {
    echo "\n=== 数据库字段检查 ===\n";
    
    try {
        // 检查fa_order表新增字段
        $orderFields = db()->query("DESCRIBE fa_order");
        $orderFieldNames = array_column($orderFields, 'Field');
        
        $requiredOrderFields = [
            'deposit_deducted',
            'balance_deducted', 
            'remaining_amount',
            'payment_detail',
            'setmeal_log_id'
        ];
        
        echo "检查fa_order表字段：\n";
        foreach ($requiredOrderFields as $field) {
            if (in_array($field, $orderFieldNames)) {
                echo "✅ {$field} - 已添加\n";
            } else {
                echo "❌ {$field} - 缺失\n";
            }
        }
        
        // 检查fa_pay表新增字段
        $payFields = db()->query("DESCRIBE fa_pay");
        $payFieldNames = array_column($payFields, 'Field');
        
        $requiredPayFields = [
            'deduction_type',
            'parent_pay_id',
            'refund_amount'
        ];
        
        echo "\n检查fa_pay表字段：\n";
        foreach ($requiredPayFields as $field) {
            if (in_array($field, $payFieldNames)) {
                echo "✅ {$field} - 已添加\n";
            } else {
                echo "❌ {$field} - 缺失\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 数据库检查异常：" . $e->getMessage() . "\n";
    }
}

/**
 * 测试用户余额和押金数据
 */
function testUserData() {
    echo "\n=== 用户数据检查 ===\n";
    
    try {
        // 查询测试用户数据
        $user = db('user')->where(['id' => 1])->find();
        
        if ($user) {
            echo "测试用户信息：\n";
            echo "- 用户ID: {$user['id']}\n";
            echo "- 押金余额: {$user['deposit']}元\n";
            echo "- 账户余额: {$user['money']}元\n";
            echo "- 押金支付ID: {$user['deposit_id']}\n";
            
            // 检查是否有生效的套餐
            $setmeal = db('setmeal_log')->where([
                'user_id' => $user['id'],
                'effective_start' => ['<=', time()],
                'effective_end' => ['>=', time()],
                'deletetime' => null,
                'status' => 2,
            ])->find();
            
            if ($setmeal) {
                echo "- 套餐状态: 有生效套餐 (ID: {$setmeal['id']})\n";
            } else {
                echo "- 套餐状态: 无生效套餐\n";
            }
            
        } else {
            echo "❌ 未找到测试用户（ID=1）\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 用户数据检查异常：" . $e->getMessage() . "\n";
    }
}

// 执行测试
if (php_sapi_name() === 'cli') {
    // 命令行模式
    testDatabaseFields();
    testUserData();
    // testOrderDeduction(); // 取消注释以测试订单处理
} else {
    // Web模式
    header('Content-Type: text/plain; charset=utf-8');
    testDatabaseFields();
    testUserData();
    echo "\n注意：订单处理测试已注释，如需测试请取消注释testOrderDeduction()调用\n";
}

echo "\n=== 测试完成 ===\n";
?>
